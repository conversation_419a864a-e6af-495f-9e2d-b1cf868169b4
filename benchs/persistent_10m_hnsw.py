#!/usr/bin/env python3
"""
Persistent 10M HNSW implementation with save/load capabilities
"""

import time
import sys
import os
import gc
import psutil
import numpy as np
import random
import heapq
import math
import pickle
import json
from collections import defaultdict


def get_memory_usage():
    """Get current memory usage in GB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024 / 1024


class PersistentHNSW:
    """
    HNSW implementation with save/load capabilities for 10M vectors
    """
    
    def __init__(self, vectors=None, M=16, max_M=16, max_M0=32, ef_construction=200):
        """Initialize HNSW"""
        self.M = M
        self.max_M = max_M
        self.max_M0 = max_M0
        self.ef_construction = ef_construction
        
        # Data
        self.vectors = vectors
        self.normalized_vectors = None
        
        # Graph structure
        self.graph = {}
        self.node_levels = {}
        self.entry_point = None
        self.max_layer = 0
        
        # Metadata
        self.build_time = 0
        self.n_vectors = 0
        
    def save_index(self, filepath):
        """Save the complete HNSW index to disk"""
        print(f"Saving HNSW index to {filepath}...")
        start_time = time.time()
        
        # Prepare data for saving
        save_data = {
            'parameters': {
                'M': self.M,
                'max_M': self.max_M,
                'max_M0': self.max_M0,
                'ef_construction': self.ef_construction
            },
            'graph_structure': {
                'graph': {layer: {node: list(neighbors) for node, neighbors in layer_dict.items()} 
                         for layer, layer_dict in self.graph.items()},
                'node_levels': self.node_levels,
                'entry_point': self.entry_point,
                'max_layer': self.max_layer
            },
            'metadata': {
                'n_vectors': self.n_vectors,
                'build_time': self.build_time,
                'vector_dim': self.normalized_vectors.shape[1] if self.normalized_vectors is not None else None
            }
        }
        
        # Save normalized vectors separately (they're large)
        vectors_file = filepath.replace('.pkl', '_vectors.npy')
        if self.normalized_vectors is not None:
            np.save(vectors_file, self.normalized_vectors)
            print(f"Saved normalized vectors to {vectors_file}")
        
        # Save graph structure
        with open(filepath, 'wb') as f:
            pickle.dump(save_data, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        save_time = time.time() - start_time
        file_size_gb = os.path.getsize(filepath) / (1024**3)
        vectors_size_gb = os.path.getsize(vectors_file) / (1024**3) if os.path.exists(vectors_file) else 0
        
        print(f"Index saved successfully in {save_time:.2f} seconds")
        print(f"Graph structure size: {file_size_gb:.2f} GB")
        print(f"Vectors size: {vectors_size_gb:.2f} GB")
        print(f"Total saved size: {file_size_gb + vectors_size_gb:.2f} GB")
        
    def load_index(self, filepath):
        """Load a complete HNSW index from disk"""
        print(f"Loading HNSW index from {filepath}...")
        start_time = time.time()
        
        # Load graph structure
        with open(filepath, 'rb') as f:
            save_data = pickle.load(f)
        
        # Restore parameters
        params = save_data['parameters']
        self.M = params['M']
        self.max_M = params['max_M']
        self.max_M0 = params['max_M0']
        self.ef_construction = params['ef_construction']
        
        # Restore graph structure
        graph_data = save_data['graph_structure']
        self.graph = {layer: {node: set(neighbors) for node, neighbors in layer_dict.items()} 
                     for layer, layer_dict in graph_data['graph'].items()}
        self.node_levels = graph_data['node_levels']
        self.entry_point = graph_data['entry_point']
        self.max_layer = graph_data['max_layer']
        
        # Restore metadata
        metadata = save_data['metadata']
        self.n_vectors = metadata['n_vectors']
        self.build_time = metadata['build_time']
        
        # Load normalized vectors
        vectors_file = filepath.replace('.pkl', '_vectors.npy')
        if os.path.exists(vectors_file):
            self.normalized_vectors = np.load(vectors_file)
            print(f"Loaded normalized vectors from {vectors_file}")
        
        load_time = time.time() - start_time
        print(f"Index loaded successfully in {load_time:.2f} seconds")
        print(f"Loaded index for {self.n_vectors:,} vectors")
        print(f"Max layer: {self.max_layer}")
        print(f"Memory usage: {get_memory_usage():.2f} GB")
        
        return True
    
    def distance(self, i, j):
        """Calculate distance between two vectors"""
        return 1.0 - np.dot(self.normalized_vectors[i], self.normalized_vectors[j])
    
    def get_random_level(self):
        """Generate random level for new node"""
        level = 0
        while random.random() < 0.5 and level < 16:
            level += 1
        return level
    
    def search_layer(self, query_idx, entry_points, num_closest, layer):
        """Search for num_closest neighbors in a specific layer"""
        visited = set()
        candidates = []
        w = []
        
        # Initialize with entry points
        for ep in entry_points:
            if layer in self.graph and ep in self.graph[layer]:
                dist = self.distance(query_idx, ep)
                heapq.heappush(candidates, (dist, ep))
                heapq.heappush(w, (-dist, ep))
                visited.add(ep)
        
        if not candidates:
            return []
        
        # Search loop
        while candidates:
            current_dist, current = heapq.heappop(candidates)
            
            if w and current_dist > -w[0][0]:
                break
            
            if layer in self.graph and current in self.graph[layer]:
                for neighbor in self.graph[layer][current]:
                    if neighbor not in visited:
                        visited.add(neighbor)
                        dist = self.distance(query_idx, neighbor)
                        
                        if len(w) < num_closest:
                            heapq.heappush(candidates, (dist, neighbor))
                            heapq.heappush(w, (-dist, neighbor))
                        elif dist < -w[0][0]:
                            heapq.heappush(candidates, (dist, neighbor))
                            heapq.heappush(w, (-dist, neighbor))
                            
                            if len(w) > num_closest:
                                heapq.heappop(w)
        
        # Return results
        result = []
        temp_w = []
        while w:
            dist, node = heapq.heappop(w)
            temp_w.append((node, -dist))
        
        return sorted(temp_w, key=lambda x: x[1])[:num_closest]
    
    def select_neighbors_simple(self, candidates, M):
        """Simple neighbor selection"""
        candidates.sort(key=lambda x: x[1])
        return [node for node, dist in candidates[:M]]
    
    def insert_node(self, node_idx):
        """Insert a node into the HNSW graph"""
        level = self.get_random_level()
        self.node_levels[node_idx] = level
        
        # Initialize graph layers for this node
        for lev in range(level + 1):
            if lev not in self.graph:
                self.graph[lev] = {}
            self.graph[lev][node_idx] = set()
        
        # Update max layer and entry point
        if level > self.max_layer:
            self.max_layer = level
            self.entry_point = node_idx
        
        # Handle first node
        if self.entry_point is None:
            self.entry_point = node_idx
            self.max_layer = level
            return
        
        current_nearest = [self.entry_point]
        
        # Search from top layer down to level+1
        for lev in range(self.max_layer, level, -1):
            current_nearest = self.search_layer(node_idx, current_nearest, 1, lev)
            current_nearest = [node for node, dist in current_nearest]
        
        # Search and connect from level down to 0
        for lev in range(min(level, self.max_layer), -1, -1):
            candidates = self.search_layer(node_idx, current_nearest, self.ef_construction, lev)
            
            # Select neighbors
            max_conn = self.max_M0 if lev == 0 else self.max_M
            neighbors = self.select_neighbors_simple(candidates, max_conn)
            
            # Ensure layer exists
            if lev not in self.graph:
                self.graph[lev] = {}
            
            # Add bidirectional connections
            for neighbor in neighbors:
                if neighbor not in self.graph[lev]:
                    self.graph[lev][neighbor] = set()
                
                self.graph[lev][node_idx].add(neighbor)
                self.graph[lev][neighbor].add(node_idx)
                
                # Simple pruning if too many connections
                if len(self.graph[lev][neighbor]) > max_conn:
                    connections = list(self.graph[lev][neighbor])
                    to_remove = random.choice(connections)
                    self.graph[lev][neighbor].remove(to_remove)
                    if to_remove in self.graph[lev] and neighbor in self.graph[lev][to_remove]:
                        self.graph[lev][to_remove].remove(neighbor)
            
            current_nearest = neighbors
    
    def build_index(self, vectors, save_path=None, checkpoint_interval=100000):
        """Build HNSW index with optional saving"""
        self.vectors = vectors
        self.n_vectors = len(vectors)
        
        print(f"Building HNSW index for {self.n_vectors:,} vectors...")
        print(f"Parameters: M={self.M}, ef_construction={self.ef_construction}")
        
        # Normalize vectors
        print("Normalizing vectors...")
        start_time = time.time()
        norms = np.linalg.norm(vectors, axis=1, keepdims=True)
        self.normalized_vectors = vectors / norms
        norm_time = time.time() - start_time
        print(f"Normalization completed in {norm_time:.2f} seconds")
        
        # Build index
        build_start = time.time()
        
        for i in range(self.n_vectors):
            if i % 10000 == 0:
                elapsed = time.time() - build_start
                if i > 0:
                    speed = i / elapsed
                    eta = (self.n_vectors - i) / speed
                    print(f"Progress: {i:,}/{self.n_vectors:,} ({i/self.n_vectors*100:.2f}%) | "
                          f"Speed: {speed:.1f} vec/s | ETA: {eta/3600:.2f}h | Memory: {get_memory_usage():.1f}GB")
                else:
                    print(f"Starting insertion of {self.n_vectors:,} vectors...")
            
            self.insert_node(i)
            
            # Checkpoint saving
            if save_path and i > 0 and i % checkpoint_interval == 0:
                checkpoint_path = save_path.replace('.pkl', f'_checkpoint_{i}.pkl')
                print(f"Saving checkpoint at {i:,} vectors...")
                self.save_index(checkpoint_path)
        
        self.build_time = time.time() - build_start
        print(f"Index built in {self.build_time:.1f} seconds ({self.build_time/3600:.2f} hours)")
        
        # Save final index
        if save_path:
            self.save_index(save_path)
        
        return self.build_time
    
    def search(self, query_vector, k=10, ef_search=50):
        """Search in the index"""
        if self.entry_point is None:
            return []
        
        # Normalize query
        query_norm = query_vector / np.linalg.norm(query_vector)
        
        # Add query as temporary node
        temp_vectors = np.vstack([self.normalized_vectors, query_norm.reshape(1, -1)])
        original_vectors = self.normalized_vectors
        self.normalized_vectors = temp_vectors
        query_idx = len(original_vectors)
        
        try:
            # Search from top layer down
            current_nearest = [self.entry_point]
            for layer in range(self.max_layer, 0, -1):
                current_nearest = self.search_layer(query_idx, current_nearest, 1, layer)
                current_nearest = [node for node, dist in current_nearest]
            
            # Search layer 0
            candidates = self.search_layer(query_idx, current_nearest, max(ef_search, k), 0)
            
            # Return top k
            results = [(dist, idx) for idx, dist in candidates[:k]]
            return results
            
        finally:
            self.normalized_vectors = original_vectors


def check_existing_index():
    """Check if there's an existing index"""
    index_path = "hnsw_10m_index.pkl"
    vectors_path = "hnsw_10m_index_vectors.npy"
    
    if os.path.exists(index_path) and os.path.exists(vectors_path):
        index_size = os.path.getsize(index_path) / (1024**3)
        vectors_size = os.path.getsize(vectors_path) / (1024**3)
        print(f"Found existing index:")
        print(f"  Graph structure: {index_size:.2f} GB")
        print(f"  Vectors: {vectors_size:.2f} GB")
        print(f"  Total: {index_size + vectors_size:.2f} GB")
        return index_path
    return None


def main():
    """Main function with save/load capabilities"""
    print("Persistent 10M HNSW Implementation")
    print("=" * 60)

    # Parameter selection
    print("\nParameter Configuration:")
    print("1. Standard config: M=16, ef_construction=200 (faster build, ~20h)")
    print("2. High quality config: M=30, ef_construction=360 (better quality, ~30-40h)")
    print("3. Custom config")

    config_choice = input("Choose configuration (1/2/3): ").strip()

    if config_choice == "1":
        M, max_M, max_M0, ef_construction = 16, 16, 32, 200
        config_name = "standard"
        index_file = "hnsw_10m_index_standard.pkl"
    elif config_choice == "2":
        M, max_M, max_M0, ef_construction = 30, 30, 60, 360
        config_name = "high_quality"
        index_file = "hnsw_10m_index_high_quality.pkl"
    elif config_choice == "3":
        try:
            M = int(input("Enter M (connections per node, default 16): ") or "16")
            max_M = M
            max_M0 = M * 2
            ef_construction = int(input("Enter ef_construction (default 200): ") or "200")
            config_name = f"custom_M{M}_ef{ef_construction}"
            index_file = f"hnsw_10m_index_{config_name}.pkl"
        except ValueError:
            print("Invalid input, using standard config")
            M, max_M, max_M0, ef_construction = 16, 16, 32, 200
            config_name = "standard"
            index_file = "hnsw_10m_index_standard.pkl"
    else:
        print("Invalid choice, using standard config")
        M, max_M, max_M0, ef_construction = 16, 16, 32, 200
        config_name = "standard"
        index_file = "hnsw_10m_index_standard.pkl"

    print(f"\nUsing {config_name} config: M={M}, ef_construction={ef_construction}")

    # Check for existing index with this config
    if os.path.exists(index_file):
        vectors_file = index_file.replace('.pkl', '_vectors.npy')
        if os.path.exists(vectors_file):
            index_size = os.path.getsize(index_file) / (1024**3)
            vectors_size = os.path.getsize(vectors_file) / (1024**3)
            print(f"\nFound existing {config_name} index:")
            print(f"  Graph structure: {index_size:.2f} GB")
            print(f"  Vectors: {vectors_size:.2f} GB")
            print(f"  Total: {index_size + vectors_size:.2f} GB")

            response = input("Load existing index? (yes/no): ")
            if response.lower() == 'yes':
                # Load existing index
                hnsw = PersistentHNSW()
                hnsw.load_index(index_file)

                # Test search performance with different ef_search values
                print("\nTesting search performance...")
                query_vectors = np.load("../query_10m.npy")[:50]

                ef_search_values = [50, 100, 200] if config_name == "high_quality" else [50, 100]

                for ef_search in ef_search_values:
                    print(f"\nTesting with ef_search={ef_search}...")
                    search_times = []

                    for i, query in enumerate(query_vectors):
                        if i % 10 == 0:
                            print(f"  Query {i+1}/50...")

                        start_time = time.time()
                        results = hnsw.search(query, k=10, ef_search=ef_search)
                        search_time = time.time() - start_time
                        search_times.append(search_time)

                    avg_search_time = np.mean(search_times)
                    qps = 1.0 / avg_search_time

                    print(f"  ef_search={ef_search}: {avg_search_time*1000:.3f} ms, {qps:.1f} QPS")

                print(f"\nMemory usage: {get_memory_usage():.2f} GB")
                return 0

    # Build new index
    estimated_hours = 20 if config_name == "standard" else 35
    print(f"\nBuilding new 10M HNSW index with {config_name} config...")
    print(f"Estimated time: {estimated_hours} hours")
    response = input("Continue? (yes/no): ")
    if response.lower() != 'yes':
        print("Cancelled.")
        return 1

    # Load data and build
    print("Loading 10M dataset...")
    base_vectors = np.load("../base_10m.npy")

    hnsw = PersistentHNSW(M=M, max_M=max_M, max_M0=max_M0, ef_construction=ef_construction)
    hnsw.build_index(base_vectors, save_path=index_file, checkpoint_interval=100000)

    print(f"10M HNSW index ({config_name}) build completed and saved!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
