#!/usr/bin/env python3
"""
Final summary of HNSW benchmark results on Cohere 10M dataset
"""

import time
import numpy as np
from numpy_hnsw_benchmark import NumpyHNSW, load_npy_data, brute_force_search_numpy, calculate_recall


def run_final_benchmark():
    """Run final benchmark with optimized parameters"""
    
    print("=" * 80)
    print("FINAL HNSW BENCHMARK RESULTS ON COHERE 10M DATASET")
    print("=" * 80)
    
    # Load data
    print("Loading Cohere dataset...")
    max_database_size = 100000  # Use larger dataset for final test
    max_queries = 1000
    
    try:
        base_vectors = load_npy_data("../base.npy", max_vectors=max_database_size)
        query_vectors = load_npy_data("../query.npy", max_vectors=max_queries)
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    print(f"\nDataset Configuration:")
    print(f"- Database vectors: {base_vectors.shape[0]:,}")
    print(f"- Query vectors: {query_vectors.shape[0]:,}")
    print(f"- Vector dimension: {base_vectors.shape[1]}")
    print(f"- Data type: {base_vectors.dtype}")
    print(f"- Total database size: {base_vectors.nbytes / 1024 / 1024:.1f} MB")
    
    # Test configurations based on parameter tuning results
    configs = [
        {"name": "Fast (Low Recall)", "M": 8, "ef_construction": 40, "ef_search": 16},
        {"name": "Balanced", "M": 16, "ef_construction": 80, "ef_search": 32},
        {"name": "High Quality", "M": 32, "ef_construction": 160, "ef_search": 64},
    ]
    
    # Compute ground truth for evaluation
    print(f"\nComputing ground truth for evaluation...")
    max_gt_queries = min(50, len(query_vectors))
    ground_truth = []
    gt_start = time.time()
    for i in range(max_gt_queries):
        if i % 10 == 0:
            print(f"  Ground truth query {i+1}/{max_gt_queries}")
        gt = brute_force_search_numpy(query_vectors[i], base_vectors, k=10)
        ground_truth.append(gt)
    gt_time = time.time() - gt_start
    
    print(f"\nBrute Force Baseline:")
    print(f"- Time per query: {gt_time / max_gt_queries * 1000:.3f} ms")
    print(f"- QPS: {max_gt_queries / gt_time:.1f}")
    print(f"- Recall@10: 1.0000 (perfect)")
    
    print(f"\n{'='*80}")
    print("HNSW CONFIGURATION COMPARISON")
    print(f"{'='*80}")
    print(f"{'Config':<20} {'Build(s)':<10} {'Query(ms)':<12} {'Recall@10':<12} {'QPS':<10} {'Speedup':<10}")
    print("-" * 80)
    
    results = []
    
    for config in configs:
        print(f"\nTesting {config['name']} configuration...")
        
        # Build index
        start_time = time.time()
        hnsw = NumpyHNSW(
            base_vectors, 
            M=config['M'], 
            max_connections=config['M']*2, 
            ef_construction=config['ef_construction']
        )
        build_time = time.time() - start_time
        
        # Test search performance
        total_time = 0
        total_recall = 0
        
        for i in range(len(query_vectors)):
            if i % 200 == 0:
                print(f"  Query {i+1}/{len(query_vectors)}")
            
            start_time = time.time()
            hnsw_results = hnsw.search(query_vectors[i], k=10, ef_search=config['ef_search'])
            end_time = time.time()
            total_time += (end_time - start_time)
            
            # Calculate recall for ground truth queries
            if i < max_gt_queries:
                recall = calculate_recall(hnsw_results, ground_truth[i])
                total_recall += recall
        
        avg_time_ms = (total_time / len(query_vectors)) * 1000
        avg_recall = total_recall / max_gt_queries if max_gt_queries > 0 else 0
        qps = len(query_vectors) / total_time if total_time > 0 else 0
        speedup = qps / (max_gt_queries / gt_time)
        
        print(f"{config['name']:<20} {build_time:<10.2f} {avg_time_ms:<12.3f} {avg_recall:<12.4f} {qps:<10.1f} {speedup:<10.1f}x")
        
        results.append({
            'config': config['name'],
            'build_time': build_time,
            'query_time_ms': avg_time_ms,
            'recall': avg_recall,
            'qps': qps,
            'speedup': speedup,
            'parameters': config
        })
    
    # Summary and recommendations
    print(f"\n{'='*80}")
    print("SUMMARY AND RECOMMENDATIONS")
    print(f"{'='*80}")
    
    print(f"\n1. PERFORMANCE ANALYSIS:")
    for result in results:
        print(f"   {result['config']}:")
        print(f"     - Speedup: {result['speedup']:.1f}x faster than brute force")
        print(f"     - Recall: {result['recall']:.1%} of true nearest neighbors found")
        print(f"     - Throughput: {result['qps']:.0f} queries per second")
        print(f"     - Build time: {result['build_time']:.1f} seconds for {base_vectors.shape[0]:,} vectors")
    
    print(f"\n2. USE CASE RECOMMENDATIONS:")
    print(f"   - Real-time applications: Use 'Fast' config for sub-millisecond queries")
    print(f"   - General purpose: Use 'Balanced' config for good speed/accuracy trade-off")
    print(f"   - High accuracy needs: Use 'High Quality' config when recall is critical")
    
    print(f"\n3. SCALING CONSIDERATIONS:")
    print(f"   - Current test: {base_vectors.shape[0]:,} vectors")
    print(f"   - Full dataset: 1,000,000 vectors (10x larger)")
    print(f"   - Expected build time for full dataset: {max(r['build_time'] for r in results) * 10:.0f}-{max(r['build_time'] for r in results) * 20:.0f} seconds")
    print(f"   - Memory requirement for full dataset: ~3-4 GB")
    
    print(f"\n4. PRODUCTION RECOMMENDATIONS:")
    print(f"   - For production use, consider Faiss library:")
    print(f"     * Optimized C++ implementation")
    print(f"     * GPU acceleration support")
    print(f"     * Better memory efficiency")
    print(f"     * Production-tested reliability")
    print(f"   - Alternative libraries: hnswlib, Annoy, ScaNN")
    
    print(f"\n{'='*80}")
    print("BENCHMARK COMPLETED SUCCESSFULLY!")
    print(f"{'='*80}")
    
    return results


def main():
    """Main function"""
    print("Cohere 10M HNSW Benchmark - Final Summary")
    print("This benchmark demonstrates HNSW algorithm performance on real embedding data")
    print()
    
    try:
        results = run_final_benchmark()
        
        print(f"\nBenchmark files created:")
        print(f"- npy_reader.py: Custom NPY file reader")
        print(f"- numpy_hnsw_benchmark.py: Optimized HNSW implementation")
        print(f"- parameter_tuning.py: Parameter optimization script")
        print(f"- benchmark_report.md: Detailed analysis report")
        print(f"- final_summary.py: This summary script")
        
        print(f"\nTo run individual components:")
        print(f"- python3 numpy_hnsw_benchmark.py  # Main benchmark")
        print(f"- python3 parameter_tuning.py      # Parameter tuning")
        print(f"- python3 npy_reader.py           # Test data loading")
        
    except Exception as e:
        print(f"Error running benchmark: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
