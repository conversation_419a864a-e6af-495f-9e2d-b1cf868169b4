#!/usr/bin/env python3
"""
Progressive benchmark starting from smaller scales and building up to 10M
"""

import time
import sys
import os
import gc
import psutil
import numpy as np
from numpy_hnsw_benchmark import NumpyHNSW


def get_memory_usage():
    """Get current memory usage in GB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024 / 1024


def load_progressive_data(max_vectors):
    """Load data progressively"""
    print(f"Loading {max_vectors:,} vectors from 10M dataset...")
    
    start_time = time.time()
    # Use memory mapping first to avoid loading everything
    base_vectors_mmap = np.load("../base_10m.npy", mmap_mode='r')
    
    # Load only the required subset
    base_vectors = np.array(base_vectors_mmap[:max_vectors])
    query_vectors = np.load("../query_10m.npy")[:100]  # Use 100 queries
    
    load_time = time.time() - start_time
    print(f"Data loaded in {load_time:.2f} seconds")
    print(f"Base vectors: {base_vectors.shape}")
    print(f"Query vectors: {query_vectors.shape}")
    print(f"Memory usage: {get_memory_usage():.2f} GB")
    
    return base_vectors, query_vectors


def quick_hnsw_test(base_vectors, query_vectors, scale_name):
    """Quick HNSW test for a given scale"""
    print(f"\n{'='*50}")
    print(f"Testing {scale_name}: {len(base_vectors):,} vectors")
    print(f"{'='*50}")
    
    # Build index with optimized parameters for large scale
    print("Building HNSW index...")
    start_time = time.time()
    
    # Use smaller M and ef_construction for faster building on large datasets
    M = 8 if len(base_vectors) > 1000000 else 16
    ef_construction = 40 if len(base_vectors) > 1000000 else 80
    
    hnsw = NumpyHNSW(
        base_vectors, 
        M=M, 
        max_connections=M*2, 
        ef_construction=ef_construction
    )
    
    build_time = time.time() - start_time
    print(f"Index built in {build_time:.2f} seconds ({build_time/60:.1f} minutes)")
    print(f"Memory usage after build: {get_memory_usage():.2f} GB")
    
    # Test search performance
    print("Testing search performance...")
    ef_search = 32
    k = 10
    
    search_times = []
    num_test_queries = min(50, len(query_vectors))  # Limit queries for large datasets
    
    for i in range(num_test_queries):
        if i % 10 == 0:
            print(f"  Query {i+1}/{num_test_queries}")
        
        start_time = time.time()
        results = hnsw.search(query_vectors[i], k=k, ef_search=ef_search)
        search_time = time.time() - start_time
        search_times.append(search_time)
    
    avg_search_time = np.mean(search_times)
    qps = 1.0 / avg_search_time if avg_search_time > 0 else 0
    
    print(f"\nResults for {scale_name}:")
    print(f"  Build time: {build_time:.2f} seconds")
    print(f"  Average search time: {avg_search_time*1000:.3f} ms")
    print(f"  QPS: {qps:.1f}")
    print(f"  Memory usage: {get_memory_usage():.2f} GB")
    
    # Calculate index statistics
    total_connections = sum(len(connections) for connections in hnsw.graph.values())
    avg_connections = total_connections / len(hnsw.graph) / 2
    print(f"  Average connections per node: {avg_connections:.2f}")
    
    result = {
        'scale_name': scale_name,
        'num_vectors': len(base_vectors),
        'build_time': build_time,
        'avg_search_time_ms': avg_search_time * 1000,
        'qps': qps,
        'memory_gb': get_memory_usage(),
        'avg_connections': avg_connections,
        'M': M,
        'ef_construction': ef_construction
    }
    
    # Clean up
    del hnsw
    gc.collect()
    
    return result


def run_progressive_benchmark():
    """Run progressive benchmark from small to large scales"""
    print("PROGRESSIVE 10M COHERE BENCHMARK")
    print("=" * 60)
    print(f"System info:")
    print(f"  Total memory: {psutil.virtual_memory().total / 1024**3:.1f} GB")
    print(f"  Available memory: {psutil.virtual_memory().available / 1024**3:.1f} GB")
    print(f"  CPU cores: {psutil.cpu_count()}")
    
    # Progressive test scales
    test_scales = [
        {"name": "100K", "size": 100000},
        {"name": "500K", "size": 500000},
        {"name": "1M", "size": 1000000},
        {"name": "2M", "size": 2000000},
        {"name": "5M", "size": 5000000},
    ]
    
    # Check available memory and adjust scales
    available_gb = psutil.virtual_memory().available / 1024**3
    if available_gb < 50:  # Less than 50GB available
        print(f"Limited memory detected ({available_gb:.1f} GB), reducing test scales")
        test_scales = test_scales[:3]  # Only test up to 1M
    elif available_gb < 100:  # Less than 100GB available
        test_scales = test_scales[:4]  # Only test up to 2M
    
    results = []
    
    for scale_config in test_scales:
        try:
            print(f"\n{'='*60}")
            print(f"LOADING {scale_config['name']} SCALE")
            print(f"{'='*60}")
            
            # Load data for this scale
            base_vectors, query_vectors = load_progressive_data(scale_config['size'])
            
            # Run test
            result = quick_hnsw_test(base_vectors, query_vectors, scale_config['name'])
            results.append(result)
            
            # Clean up before next test
            del base_vectors, query_vectors
            gc.collect()
            
            # Check memory usage
            current_memory = get_memory_usage()
            available_memory = psutil.virtual_memory().available / 1024**3
            
            print(f"\nMemory status after {scale_config['name']}:")
            print(f"  Current usage: {current_memory:.2f} GB")
            print(f"  Available: {available_memory:.2f} GB")
            
            # Stop if memory is getting low
            if available_memory < 20:  # Less than 20GB available
                print("Warning: Low memory, stopping progressive test")
                break
                
        except Exception as e:
            print(f"Error testing {scale_config['name']}: {e}")
            break
    
    # Print summary
    print(f"\n{'='*80}")
    print("PROGRESSIVE BENCHMARK SUMMARY")
    print(f"{'='*80}")
    
    print(f"{'Scale':<8} {'Vectors':<10} {'Build(s)':<10} {'Search(ms)':<12} {'QPS':<8} {'Memory(GB)':<12} {'Connections':<12}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['scale_name']:<8} {result['num_vectors']:<10,} {result['build_time']:<10.1f} "
              f"{result['avg_search_time_ms']:<12.3f} {result['qps']:<8.1f} {result['memory_gb']:<12.1f} "
              f"{result['avg_connections']:<12.1f}")
    
    # Analyze scaling trends
    if len(results) > 1:
        print(f"\nScaling Analysis:")
        base_result = results[0]
        
        for result in results[1:]:
            size_ratio = result['num_vectors'] / base_result['num_vectors']
            build_ratio = result['build_time'] / base_result['build_time']
            search_ratio = result['avg_search_time_ms'] / base_result['avg_search_time_ms']
            memory_ratio = result['memory_gb'] / base_result['memory_gb']
            
            print(f"  {result['scale_name']} vs {base_result['scale_name']}:")
            print(f"    Size: {size_ratio:.1f}x, Build time: {build_ratio:.1f}x, "
                  f"Search time: {search_ratio:.1f}x, Memory: {memory_ratio:.1f}x")
    
    return results


def main():
    """Main function"""
    print("Cohere 10M Progressive HNSW Benchmark")
    print("Testing HNSW performance at increasing scales")
    
    try:
        results = run_progressive_benchmark()
        
        print(f"\nProgressive benchmark completed!")
        print(f"Tested {len(results)} different scales")
        
        # Recommendations based on results
        if results:
            print(f"\nRecommendations:")
            best_qps = max(results, key=lambda x: x['qps'])
            print(f"  Best QPS: {best_qps['qps']:.1f} at {best_qps['scale_name']} scale")
            
            if len(results) > 1:
                largest_scale = results[-1]
                print(f"  Largest scale tested: {largest_scale['scale_name']} ({largest_scale['num_vectors']:,} vectors)")
                print(f"  Build time for largest: {largest_scale['build_time']/60:.1f} minutes")
                print(f"  Memory usage for largest: {largest_scale['memory_gb']:.1f} GB")
        
    except Exception as e:
        print(f"Progressive benchmark failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
