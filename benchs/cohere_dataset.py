#!/usr/bin/env python3
# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

import os
import numpy as np
import pandas as pd
from typing import List, Optional

class DatasetCohere10M:
    """
    Dataset class for Cohere 10M dataset stored in parquet format
    """
    
    def __init__(self, dataset_path: str = "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m"):
        """
        Initialize Cohere 10M dataset
        
        Args:
            dataset_path: Path to the cohere_large_10m directory
        """
        self.dataset_path = dataset_path
        self.d = 1024  # Cohere embedding dimension
        self.metric = 'L2'  # Default metric
        
        # Check if dataset path exists
        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"Dataset path not found: {dataset_path}")
        
        # Get all training shard files
        self.train_files = []
        for i in range(10):  # 10 shards: 00-of-10 to 09-of-10
            train_file = os.path.join(dataset_path, f"shuffle_train-{i:02d}-of-10.parquet")
            if os.path.exists(train_file):
                self.train_files.append(train_file)
        
        self.test_file = os.path.join(dataset_path, "test.parquet")
        self.neighbors_file = os.path.join(dataset_path, "neighbors.parquet")
        
        # Load metadata to get sizes
        self._load_metadata()
        
    def _load_metadata(self):
        """Load metadata to determine dataset sizes"""
        # Get query size from test file
        if os.path.exists(self.test_file):
            test_df = pd.read_parquet(self.test_file)
            self.nq = len(test_df)
        else:
            self.nq = 0
            
        # Get database size from training files
        self.nb = 0
        for train_file in self.train_files:
            if os.path.exists(train_file):
                train_df = pd.read_parquet(train_file)
                self.nb += len(train_df)
        
        # Training size is same as database size for this dataset
        self.nt = self.nb
        
        print(f"Dataset loaded: d={self.d}, nb={self.nb}, nq={self.nq}, nt={self.nt}")
    
    def _parquet_to_numpy(self, file_path: str, max_rows: Optional[int] = None) -> np.ndarray:
        """
        Convert parquet file to numpy array
        
        Args:
            file_path: Path to parquet file
            max_rows: Maximum number of rows to load
            
        Returns:
            numpy array of embeddings
        """
        print(f"Loading {file_path}")
        df = pd.read_parquet(file_path)
        
        if max_rows is not None:
            df = df.head(max_rows)
        
        # Find the embedding column (should be a list/array column)
        embedding_col = None
        for col in df.columns:
            first_val = df[col].iloc[0]
            if isinstance(first_val, (list, np.ndarray)):
                embedding_col = col
                break
        
        if embedding_col is None:
            raise ValueError(f"No embedding column found in {file_path}")
        
        # Convert to numpy array
        embeddings = df[embedding_col].tolist()
        arr = np.array(embeddings, dtype=np.float32)
        
        print(f"Loaded {arr.shape[0]} vectors of dimension {arr.shape[1]}")
        return arr
    
    def get_queries(self) -> np.ndarray:
        """Return query vectors as (nq, d) array"""
        if not os.path.exists(self.test_file):
            raise FileNotFoundError(f"Test file not found: {self.test_file}")
        return self._parquet_to_numpy(self.test_file)
    
    def get_database(self, max_vectors: Optional[int] = None) -> np.ndarray:
        """
        Return database vectors as (nb, d) array
        
        Args:
            max_vectors: Maximum number of vectors to load (for testing)
        """
        if not self.train_files:
            raise FileNotFoundError("No training files found")
        
        arrays = []
        total_loaded = 0
        
        for train_file in self.train_files:
            if max_vectors is not None and total_loaded >= max_vectors:
                break
                
            remaining = None
            if max_vectors is not None:
                remaining = max_vectors - total_loaded
                
            arr = self._parquet_to_numpy(train_file, remaining)
            arrays.append(arr)
            total_loaded += arr.shape[0]
        
        if not arrays:
            raise ValueError("No data loaded")
        
        result = np.vstack(arrays)
        print(f"Total database vectors loaded: {result.shape}")
        return result
    
    def get_train(self, maxtrain: Optional[int] = None) -> np.ndarray:
        """
        Return training vectors as (nt, d) array
        For this dataset, training vectors are same as database vectors
        """
        return self.get_database(max_vectors=maxtrain)
    
    def get_groundtruth(self, k: Optional[int] = None) -> np.ndarray:
        """
        Return ground truth neighbors
        
        Args:
            k: Number of neighbors to return
        """
        if not os.path.exists(self.neighbors_file):
            raise FileNotFoundError(f"Neighbors file not found: {self.neighbors_file}")
        
        print(f"Loading ground truth from {self.neighbors_file}")
        df = pd.read_parquet(self.neighbors_file)
        
        # Find the neighbors column
        neighbors_col = None
        for col in df.columns:
            first_val = df[col].iloc[0]
            if isinstance(first_val, (list, np.ndarray)):
                neighbors_col = col
                break
        
        if neighbors_col is None:
            raise ValueError("No neighbors column found")
        
        # Convert to numpy array
        neighbors = df[neighbors_col].tolist()
        gt = np.array(neighbors, dtype=np.int32)
        
        if k is not None and k < gt.shape[1]:
            gt = gt[:, :k]
        
        print(f"Ground truth shape: {gt.shape}")
        return gt
    
    def __str__(self):
        return (f"Cohere 10M dataset in dimension {self.d}, with metric {self.metric}, "
                f"size: Q {self.nq} B {self.nb} T {self.nt}")


def test_dataset():
    """Test function to verify dataset loading"""
    try:
        ds = DatasetCohere10M()
        print(ds)
        
        # Test loading small samples
        print("\nTesting query loading...")
        xq = ds.get_queries()
        print(f"Queries shape: {xq.shape}")
        
        print("\nTesting database loading (first 1000 vectors)...")
        xb = ds.get_database(max_vectors=1000)
        print(f"Database shape: {xb.shape}")
        
        print("\nTesting ground truth loading...")
        gt = ds.get_groundtruth(k=10)
        print(f"Ground truth shape: {gt.shape}")
        
        print("\nDataset test completed successfully!")
        
    except Exception as e:
        print(f"Dataset test failed: {e}")
        raise


if __name__ == "__main__":
    test_dataset()
