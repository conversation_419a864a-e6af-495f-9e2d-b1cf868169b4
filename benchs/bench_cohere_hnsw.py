#!/usr/bin/env python3
# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

import time
import sys
import numpy as np
import os
import argparse

# Try to import faiss, if not available, try to build it
try:
    import faiss
    print("Faiss imported successfully")
except ImportError:
    print("Faiss not found, trying to build from source...")
    # Add the faiss python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'faiss', 'python'))
    try:
        import faiss
        print("Faiss imported from source")
    except ImportError:
        print("Failed to import faiss. Please install faiss first.")
        sys.exit(1)


def evaluate_index(index, xq, gt, k):
    """
    Evaluate index performance
    
    Args:
        index: Faiss index
        xq: Query vectors
        gt: Ground truth neighbors
        k: Number of neighbors to search
    
    Returns:
        Tuple of (query_time_ms, recall_at_1, recall_at_k, missing_rate)
    """
    print(f"Searching {xq.shape[0]} queries for {k} neighbors...")
    
    t0 = time.time()
    D, I = index.search(xq, k)
    t1 = time.time()
    
    nq = xq.shape[0]
    query_time_ms = (t1 - t0) * 1000.0 / nq
    
    # Calculate metrics
    missing_rate = (I == -1).sum() / float(k * nq)
    
    # Recall@1
    recall_at_1 = (I[:, :1] == gt[:, :1]).sum() / float(nq)
    
    # Recall@k
    recall_at_k = 0.0
    for i in range(nq):
        intersection = np.intersect1d(I[i], gt[i, :k])
        recall_at_k += len(intersection) / k
    recall_at_k /= nq
    
    return query_time_ms, recall_at_1, recall_at_k, missing_rate


def bench_hnsw_flat(ds, args):
    """Benchmark HNSW Flat index"""
    print("=" * 60)
    print("Testing HNSW Flat")
    print("=" * 60)
    
    # Load data
    print("Loading database vectors...")
    if args.max_database_size:
        xb = ds.get_database(max_vectors=args.max_database_size)
    else:
        xb = ds.get_database()
    
    print("Loading query vectors...")
    xq = ds.get_queries()
    if args.max_queries:
        xq = xq[:args.max_queries]
    
    print("Loading ground truth...")
    gt = ds.get_groundtruth(k=args.k)
    if args.max_queries:
        gt = gt[:args.max_queries]
    
    d = xb.shape[1]
    print(f"Dataset: {xb.shape[0]} database vectors, {xq.shape[0]} queries, dimension {d}")
    
    # Create HNSW index
    print(f"Creating HNSW index with M={args.hnsw_m}")
    index = faiss.IndexHNSWFlat(d, args.hnsw_m)
    
    # Set construction parameters
    index.hnsw.efConstruction = args.ef_construction
    index.verbose = args.verbose
    
    print(f"Index parameters: M={args.hnsw_m}, efConstruction={args.ef_construction}")
    
    # Add vectors to index
    print("Adding vectors to index...")
    t0 = time.time()
    index.add(xb)
    t1 = time.time()
    
    build_time = t1 - t0
    print(f"Index built in {build_time:.2f} seconds ({xb.shape[0]/build_time:.0f} vectors/sec)")
    
    # Test different efSearch values
    print("\nTesting different efSearch values:")
    print("efSearch | bounded_queue | time(ms) | R@1    | R@k    | missing")
    print("-" * 65)
    
    results = []
    
    for efSearch in args.ef_search_values:
        for bounded_queue in [True, False]:
            index.hnsw.efSearch = efSearch
            index.hnsw.search_bounded_queue = bounded_queue
            
            query_time_ms, recall_at_1, recall_at_k, missing_rate = evaluate_index(
                index, xq, gt, args.k
            )
            
            print(f"{efSearch:8d} | {str(bounded_queue):13s} | {query_time_ms:8.3f} | {recall_at_1:.4f} | {recall_at_k:.4f} | {missing_rate:.4f}")
            
            results.append({
                'efSearch': efSearch,
                'bounded_queue': bounded_queue,
                'query_time_ms': query_time_ms,
                'recall_at_1': recall_at_1,
                'recall_at_k': recall_at_k,
                'missing_rate': missing_rate,
                'build_time': build_time
            })
    
    return results


def bench_hnsw_sq(ds, args):
    """Benchmark HNSW with Scalar Quantizer"""
    print("=" * 60)
    print("Testing HNSW with Scalar Quantizer")
    print("=" * 60)
    
    # Load data
    print("Loading database vectors...")
    if args.max_database_size:
        xb = ds.get_database(max_vectors=args.max_database_size)
    else:
        xb = ds.get_database()
    
    print("Loading training vectors...")
    if args.max_train_size:
        xt = ds.get_train(maxtrain=args.max_train_size)
    else:
        xt = ds.get_train(maxtrain=100000)  # Use 100k for training
    
    print("Loading query vectors...")
    xq = ds.get_queries()
    if args.max_queries:
        xq = xq[:args.max_queries]
    
    print("Loading ground truth...")
    gt = ds.get_groundtruth(k=args.k)
    if args.max_queries:
        gt = gt[:args.max_queries]
    
    d = xb.shape[1]
    print(f"Dataset: {xb.shape[0]} database vectors, {xq.shape[0]} queries, dimension {d}")
    
    # Create HNSW with scalar quantizer
    print(f"Creating HNSW+SQ index with M={args.hnsw_m}")
    index = faiss.IndexHNSWSQ(d, faiss.ScalarQuantizer.QT_8bit, args.hnsw_m)
    
    # Set construction parameters
    index.hnsw.efConstruction = args.ef_construction
    index.verbose = args.verbose
    
    print(f"Index parameters: M={args.hnsw_m}, efConstruction={args.ef_construction}, quantizer=8bit")
    
    # Train the scalar quantizer
    print("Training scalar quantizer...")
    t0 = time.time()
    index.train(xt)
    t1 = time.time()
    train_time = t1 - t0
    print(f"Training completed in {train_time:.2f} seconds")
    
    # Add vectors to index
    print("Adding vectors to index...")
    t0 = time.time()
    index.add(xb)
    t1 = time.time()
    
    build_time = t1 - t0
    print(f"Index built in {build_time:.2f} seconds ({xb.shape[0]/build_time:.0f} vectors/sec)")
    
    # Test different efSearch values
    print("\nTesting different efSearch values:")
    print("efSearch | time(ms) | R@1    | R@k    | missing")
    print("-" * 45)
    
    results = []
    
    for efSearch in args.ef_search_values:
        index.hnsw.efSearch = efSearch
        
        query_time_ms, recall_at_1, recall_at_k, missing_rate = evaluate_index(
            index, xq, gt, args.k
        )
        
        print(f"{efSearch:8d} | {query_time_ms:8.3f} | {recall_at_1:.4f} | {recall_at_k:.4f} | {missing_rate:.4f}")
        
        results.append({
            'efSearch': efSearch,
            'query_time_ms': query_time_ms,
            'recall_at_1': recall_at_1,
            'recall_at_k': recall_at_k,
            'missing_rate': missing_rate,
            'train_time': train_time,
            'build_time': build_time
        })
    
    return results


def main():
    parser = argparse.ArgumentParser(description='Benchmark HNSW on Cohere 10M dataset')
    
    # Dataset parameters
    parser.add_argument('--dataset-path', default='/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m',
                        help='Path to Cohere 10M dataset')
    parser.add_argument('--max-database-size', type=int, default=None,
                        help='Maximum number of database vectors to use (for testing)')
    parser.add_argument('--max-queries', type=int, default=None,
                        help='Maximum number of queries to use')
    parser.add_argument('--max-train-size', type=int, default=None,
                        help='Maximum number of training vectors to use')
    
    # Search parameters
    parser.add_argument('--k', type=int, default=10,
                        help='Number of nearest neighbors to search')
    
    # HNSW parameters
    parser.add_argument('--hnsw-m', type=int, default=32,
                        help='HNSW M parameter (number of connections)')
    parser.add_argument('--ef-construction', type=int, default=40,
                        help='HNSW efConstruction parameter')
    parser.add_argument('--ef-search-values', type=int, nargs='+', 
                        default=[16, 32, 64, 128, 256],
                        help='List of efSearch values to test')
    
    # Test selection
    parser.add_argument('--tests', nargs='+', default=['hnsw_flat'],
                        choices=['hnsw_flat', 'hnsw_sq'],
                        help='Which tests to run')
    
    # Other parameters
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    
    args = parser.parse_args()
    
    print("Cohere 10M HNSW Benchmark")
    print("=" * 60)
    print(f"Arguments: {args}")
    print()
    
    # Load dataset
    print("Loading Cohere 10M dataset...")
    try:
        ds = DatasetCohere10M(args.dataset_path)
        print(ds)
        print()
    except Exception as e:
        print(f"Error loading dataset: {e}")
        return 1
    
    # Run tests
    all_results = {}
    
    if 'hnsw_flat' in args.tests:
        try:
            results = bench_hnsw_flat(ds, args)
            all_results['hnsw_flat'] = results
        except Exception as e:
            print(f"Error in HNSW Flat test: {e}")
    
    if 'hnsw_sq' in args.tests:
        try:
            results = bench_hnsw_sq(ds, args)
            all_results['hnsw_sq'] = results
        except Exception as e:
            print(f"Error in HNSW SQ test: {e}")
    
    print("\nBenchmark completed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
