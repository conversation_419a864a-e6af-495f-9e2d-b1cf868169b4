#!/usr/bin/env python3
"""
Large-scale HNSW benchmark on full Cohere 10M dataset
This script is designed for testing with the complete 10M vector dataset
"""

import time
import sys
import os
import gc
import psutil
import numpy as np
from numpy_hnsw_benchmark import NumpyHNSW, brute_force_search_numpy, calculate_recall


def get_memory_usage():
    """Get current memory usage in GB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024 / 1024


def load_10m_data(max_base_vectors=None, max_queries=None):
    """Load the full 10M dataset"""
    print("Loading Cohere 10M dataset...")
    
    # Load base vectors
    print("Loading base vectors...")
    start_time = time.time()
    base_vectors = np.load("../base_10m.npy", mmap_mode='r')  # Use memory mapping for large files
    if max_base_vectors is not None:
        base_vectors = base_vectors[:max_base_vectors]
    else:
        # Load into memory for better performance
        print("Loading full dataset into memory...")
        base_vectors = np.array(base_vectors)
    
    load_time = time.time() - start_time
    print(f"Base vectors loaded in {load_time:.2f} seconds")
    print(f"Shape: {base_vectors.shape}")
    print(f"Memory usage: {get_memory_usage():.2f} GB")
    
    # Load query vectors
    print("Loading query vectors...")
    query_vectors = np.load("../query_10m.npy")
    if max_queries is not None:
        query_vectors = query_vectors[:max_queries]
    
    print(f"Query vectors shape: {query_vectors.shape}")
    print(f"Total memory usage: {get_memory_usage():.2f} GB")
    
    return base_vectors, query_vectors


class LargeScaleHNSW:
    """HNSW implementation optimized for large-scale datasets"""
    
    def __init__(self, vectors, M=16, max_connections=32, ef_construction=200, progress_interval=10000):
        """
        Initialize HNSW for large-scale data
        
        Args:
            vectors: numpy array of vectors
            M: Number of bi-directional links
            max_connections: Maximum connections per node
            ef_construction: Dynamic candidate list size during construction
            progress_interval: Progress reporting interval
        """
        self.vectors = vectors
        self.M = M
        self.max_connections = max_connections
        self.ef_construction = ef_construction
        self.progress_interval = progress_interval
        
        print(f"Initializing HNSW for {len(vectors):,} vectors...")
        print(f"Vector dimension: {vectors.shape[1]}")
        print(f"Memory usage before normalization: {get_memory_usage():.2f} GB")
        
        # Normalize vectors for cosine similarity
        print("Normalizing vectors...")
        start_time = time.time()
        norms = np.linalg.norm(vectors, axis=1, keepdims=True)
        self.normalized_vectors = vectors / norms
        norm_time = time.time() - start_time
        print(f"Normalization completed in {norm_time:.2f} seconds")
        print(f"Memory usage after normalization: {get_memory_usage():.2f} GB")
        
        # Initialize graph
        self.graph = {}
        
    def build_index(self):
        """Build HNSW index with progress tracking"""
        print(f"\nBuilding HNSW index...")
        print(f"Parameters: M={self.M}, max_connections={self.max_connections}, ef_construction={self.ef_construction}")
        
        start_time = time.time()
        n_vectors = len(self.vectors)
        
        # Initialize graph
        for i in range(n_vectors):
            self.graph[i] = set()
        
        # Build connections with progress tracking
        for i in range(n_vectors):
            if i % self.progress_interval == 0:
                elapsed = time.time() - start_time
                if i > 0:
                    eta = elapsed * (n_vectors - i) / i
                    print(f"Processing vector {i+1:,}/{n_vectors:,} ({i/n_vectors*100:.1f}%) - "
                          f"Elapsed: {elapsed:.1f}s, ETA: {eta:.1f}s, Memory: {get_memory_usage():.2f} GB")
                else:
                    print(f"Processing vector {i+1:,}/{n_vectors:,}")
            
            # Calculate similarities to previous vectors (batch processing for efficiency)
            if i > 0:
                batch_size = min(10000, i)  # Process in batches to manage memory
                similarities = []
                
                for start_idx in range(0, i, batch_size):
                    end_idx = min(start_idx + batch_size, i)
                    batch_sims = np.dot(self.normalized_vectors[start_idx:end_idx], 
                                       self.normalized_vectors[i])
                    similarities.extend(zip(batch_sims, range(start_idx, end_idx)))
                
                # Get top M most similar vectors
                similarities.sort(reverse=True)  # Sort by similarity (descending)
                top_connections = min(self.M, len(similarities))
                
                # Add bidirectional connections
                for sim, j in similarities[:top_connections]:
                    self.graph[i].add(j)
                    self.graph[j].add(i)
                    
                    # Limit connections per node
                    if len(self.graph[j]) > self.max_connections:
                        # Remove connection with lowest similarity
                        connections = list(self.graph[j])
                        if len(connections) > 1:
                            # Calculate similarities for all connections of node j
                            conn_sims = np.dot(self.normalized_vectors[connections], 
                                             self.normalized_vectors[j])
                            worst_idx = connections[np.argmin(conn_sims)]
                            self.graph[j].remove(worst_idx)
                            self.graph[worst_idx].discard(j)
        
        build_time = time.time() - start_time
        print(f"\nIndex built in {build_time:.2f} seconds ({build_time/60:.1f} minutes)")
        print(f"Final memory usage: {get_memory_usage():.2f} GB")
        
        # Print statistics
        total_connections = sum(len(connections) for connections in self.graph.values())
        avg_connections = total_connections / len(self.graph) / 2
        print(f"Average connections per node: {avg_connections:.2f}")
        print(f"Total edges in graph: {total_connections // 2:,}")
        
        return build_time
    
    def search(self, query_vector, k=10, ef_search=50):
        """Search for k nearest neighbors"""
        if len(self.vectors) == 0:
            return []
        
        # Normalize query vector
        query_norm = query_vector / np.linalg.norm(query_vector)
        
        # Start with random entry point
        entry_point = np.random.randint(0, len(self.vectors))
        
        # Greedy search
        visited = set()
        candidates = []
        
        # Calculate similarity to entry point
        sim = np.dot(self.normalized_vectors[entry_point], query_norm)
        candidates.append((-sim, entry_point))  # Use negative for min-heap behavior
        visited.add(entry_point)
        
        # Expand search
        for _ in range(ef_search):
            if not candidates:
                break
            
            # Get best candidate
            candidates.sort()
            current_sim, current_node = candidates.pop(0)
            
            # Explore neighbors
            for neighbor in self.graph.get(current_node, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    neighbor_sim = np.dot(self.normalized_vectors[neighbor], query_norm)
                    candidates.append((-neighbor_sim, neighbor))
            
            # Keep only best candidates
            candidates.sort()
            candidates = candidates[:ef_search]
        
        # Return top k results (convert back to positive similarities)
        candidates.sort()
        results = [(-sim, idx) for sim, idx in candidates[:k]]
        return results


def run_large_scale_benchmark():
    """Run benchmark on different scales"""
    print("=" * 80)
    print("COHERE 10M LARGE-SCALE HNSW BENCHMARK")
    print("=" * 80)
    
    # Test different scales
    test_scales = [
        {"name": "1M Scale Test", "base_size": 1000000, "queries": 100},
        {"name": "2M Scale Test", "base_size": 2000000, "queries": 100},
        {"name": "5M Scale Test", "base_size": 5000000, "queries": 100},
        {"name": "10M Full Scale", "base_size": None, "queries": 100},  # Full dataset
    ]
    
    results = []
    
    for test_config in test_scales:
        print(f"\n{'='*60}")
        print(f"Running {test_config['name']}")
        print(f"{'='*60}")
        
        try:
            # Load data
            base_vectors, query_vectors = load_10m_data(
                max_base_vectors=test_config['base_size'],
                max_queries=test_config['queries']
            )
            
            actual_base_size = len(base_vectors)
            print(f"Actual dataset size: {actual_base_size:,} vectors")
            
            # Build index
            hnsw = LargeScaleHNSW(
                base_vectors, 
                M=16, 
                max_connections=32, 
                ef_construction=200,
                progress_interval=max(1000, actual_base_size // 100)
            )
            
            build_time = hnsw.build_index()
            
            # Test search performance
            print(f"\nTesting search performance...")
            ef_search_values = [32, 64, 128]
            
            for ef_search in ef_search_values:
                print(f"\nTesting ef_search = {ef_search}")
                
                search_times = []
                for i, query in enumerate(query_vectors):
                    if i % 20 == 0:
                        print(f"  Query {i+1}/{len(query_vectors)}")
                    
                    start_time = time.time()
                    results_hnsw = hnsw.search(query, k=10, ef_search=ef_search)
                    search_time = time.time() - start_time
                    search_times.append(search_time)
                
                avg_search_time = np.mean(search_times)
                qps = 1.0 / avg_search_time if avg_search_time > 0 else 0
                
                print(f"  Average search time: {avg_search_time*1000:.3f} ms")
                print(f"  QPS: {qps:.1f}")
                
                results.append({
                    'scale': test_config['name'],
                    'base_size': actual_base_size,
                    'build_time': build_time,
                    'ef_search': ef_search,
                    'avg_search_time_ms': avg_search_time * 1000,
                    'qps': qps
                })
            
            # Clean up memory
            del hnsw
            del base_vectors
            gc.collect()
            
        except Exception as e:
            print(f"Error in {test_config['name']}: {e}")
            continue
    
    # Print summary
    print(f"\n{'='*80}")
    print("LARGE-SCALE BENCHMARK SUMMARY")
    print(f"{'='*80}")
    
    print(f"{'Scale':<20} {'Size':<10} {'Build(min)':<12} {'ef_search':<10} {'Time(ms)':<10} {'QPS':<8}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['scale']:<20} {result['base_size']:<10,} {result['build_time']/60:<12.1f} "
              f"{result['ef_search']:<10} {result['avg_search_time_ms']:<10.3f} {result['qps']:<8.1f}")
    
    return results


def main():
    """Main function"""
    print("Cohere 10M Large-Scale HNSW Benchmark")
    print("This benchmark tests HNSW performance on the full 10M dataset")
    print(f"Available memory: {psutil.virtual_memory().total / 1024**3:.1f} GB")
    print(f"Available CPU cores: {psutil.cpu_count()}")
    
    try:
        results = run_large_scale_benchmark()
        
        print(f"\nBenchmark completed successfully!")
        print(f"Results saved for {len(results)} test configurations")
        
    except Exception as e:
        print(f"Benchmark failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
