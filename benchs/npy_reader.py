#!/usr/bin/env python3
"""
Simple NPY file reader without numpy dependency
This implements basic .npy file format reading
"""

import struct
import sys


def read_npy_header(file_handle):
    """Read NPY file header and return shape, dtype info"""
    # Read magic string
    magic = file_handle.read(6)
    if magic != b'\x93NUMPY':
        raise ValueError("Not a valid NPY file")
    
    # Read version
    major_version = struct.unpack('B', file_handle.read(1))[0]
    minor_version = struct.unpack('B', file_handle.read(1))[0]
    
    if major_version == 1:
        header_len = struct.unpack('<H', file_handle.read(2))[0]
    elif major_version == 2:
        header_len = struct.unpack('<I', file_handle.read(4))[0]
    else:
        raise ValueError(f"Unsupported NPY version: {major_version}.{minor_version}")
    
    # Read header
    header = file_handle.read(header_len).decode('latin1')
    
    # Parse header (simplified)
    # Format: {'descr': '<f4', 'fortran_order': False, 'shape': (1000, 1024), }
    import ast
    header_dict = ast.literal_eval(header)
    
    return header_dict


def read_npy_data(file_path, max_vectors=None):
    """Read NPY file data"""
    print(f"Reading NPY file: {file_path}")
    
    with open(file_path, 'rb') as f:
        header = read_npy_header(f)
        
        shape = header['shape']
        dtype = header['descr']
        fortran_order = header.get('fortran_order', False)
        
        print(f"Shape: {shape}")
        print(f"Dtype: {dtype}")
        print(f"Fortran order: {fortran_order}")
        
        if len(shape) != 2:
            raise ValueError(f"Expected 2D array, got shape {shape}")
        
        num_vectors, dim = shape
        
        if max_vectors is not None:
            num_vectors = min(num_vectors, max_vectors)
        
        # Determine data type
        if dtype == '<f4':  # little-endian float32
            item_size = 4
            format_char = 'f'
        elif dtype == '<f8':  # little-endian float64
            item_size = 8
            format_char = 'd'
        else:
            raise ValueError(f"Unsupported dtype: {dtype}")

        # Read data
        vectors = []
        for i in range(num_vectors):
            if i % 1000 == 0:
                print(f"Reading vector {i+1}/{num_vectors}")

            vector_data = f.read(dim * item_size)
            if len(vector_data) != dim * item_size:
                break

            # Use little-endian format
            format_string = f'<{dim}{format_char}'
            vector = list(struct.unpack(format_string, vector_data))
            vectors.append(vector)
        
        print(f"Successfully read {len(vectors)} vectors of dimension {dim}")
        return vectors


def test_npy_reader():
    """Test the NPY reader with existing files"""
    base_file = "../base.npy"
    query_file = "../query.npy"
    
    try:
        print("Testing base file reading...")
        base_vectors = read_npy_data(base_file, max_vectors=100)
        print(f"Base vectors: {len(base_vectors)} x {len(base_vectors[0]) if base_vectors else 0}")
        
        print("\nTesting query file reading...")
        query_vectors = read_npy_data(query_file, max_vectors=10)
        print(f"Query vectors: {len(query_vectors)} x {len(query_vectors[0]) if query_vectors else 0}")
        
        # Show first few values of first vector
        if base_vectors:
            print(f"\nFirst base vector (first 10 values): {base_vectors[0][:10]}")
        
        if query_vectors:
            print(f"First query vector (first 10 values): {query_vectors[0][:10]}")
        
        return base_vectors, query_vectors
        
    except Exception as e:
        print(f"Error reading NPY files: {e}")
        return None, None


if __name__ == "__main__":
    test_npy_reader()
