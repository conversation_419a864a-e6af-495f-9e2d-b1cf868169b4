#!/usr/bin/env python3
"""
Multi-process HNSW benchmark to compare with multi-threading
"""

import time
import multiprocessing as mp
import numpy as np
from numpy_hnsw_benchmark import NumpyHNSW, load_npy_data


def worker_process(worker_id, base_vectors, queries, results_queue, M=16, ef_construction=80, ef_search=32, k=10):
    """Worker process function"""
    try:
        # Each process builds its own index
        print(f"Worker {worker_id}: Building HNSW index...")
        start_build = time.time()
        hnsw = NumpyHNSW(base_vectors, M=M, max_connections=M*2, ef_construction=ef_construction)
        build_time = time.time() - start_build
        
        # Process queries
        process_results = []
        start_search = time.time()
        
        for i, query in enumerate(queries):
            query_start = time.time()
            search_results = hnsw.search(query, k=k, ef_search=ef_search)
            query_time = time.time() - query_start
            
            process_results.append({
                'worker_id': worker_id,
                'query_id': i,
                'query_time': query_time,
                'results_count': len(search_results)
            })
        
        total_search_time = time.time() - start_search
        
        # Send results back
        results_queue.put({
            'worker_id': worker_id,
            'build_time': build_time,
            'total_search_time': total_search_time,
            'num_queries': len(queries),
            'query_results': process_results,
            'success': True
        })
        
    except Exception as e:
        results_queue.put({
            'worker_id': worker_id,
            'error': str(e),
            'success': False
        })


def multiprocess_benchmark(base_vectors, query_vectors, num_processes, queries_per_process):
    """Run multi-process benchmark"""
    print(f"\n{'='*60}")
    print(f"Multi-Process Test: {num_processes} processes, {queries_per_process} queries each")
    print(f"{'='*60}")
    
    # Prepare queries for each process
    total_queries = num_processes * queries_per_process
    process_queries = []
    
    for i in range(num_processes):
        start_idx = i * queries_per_process
        end_idx = start_idx + queries_per_process
        # Cycle through available queries if needed
        proc_queries = []
        for j in range(queries_per_process):
            query_idx = (start_idx + j) % len(query_vectors)
            proc_queries.append(query_vectors[query_idx])
        process_queries.append(proc_queries)
    
    # Create result queue
    results_queue = mp.Queue()
    
    # Start processes
    processes = []
    start_time = time.time()
    
    for i in range(num_processes):
        p = mp.Process(
            target=worker_process,
            args=(i, base_vectors, process_queries[i], results_queue)
        )
        p.start()
        processes.append(p)
    
    # Collect results
    results = []
    for i in range(num_processes):
        result = results_queue.get()
        results.append(result)
    
    # Wait for all processes to complete
    for p in processes:
        p.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Analyze results
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    if successful_results:
        total_build_time = sum(r['build_time'] for r in successful_results)
        avg_build_time = total_build_time / len(successful_results)
        
        all_query_results = []
        for r in successful_results:
            all_query_results.extend(r['query_results'])
        
        if all_query_results:
            avg_query_time = np.mean([qr['query_time'] for qr in all_query_results])
            min_query_time = np.min([qr['query_time'] for qr in all_query_results])
            max_query_time = np.max([qr['query_time'] for qr in all_query_results])
            
            total_successful_queries = len(all_query_results)
            # Calculate QPS based on search time only (excluding build time)
            total_search_time = sum(r['total_search_time'] for r in successful_results)
            avg_search_time = total_search_time / len(successful_results)
            qps = total_successful_queries / avg_search_time if avg_search_time > 0 else 0
        else:
            avg_query_time = min_query_time = max_query_time = 0
            total_successful_queries = 0
            qps = 0
    else:
        avg_build_time = avg_query_time = min_query_time = max_query_time = 0
        total_successful_queries = 0
        qps = 0
    
    print(f"Results:")
    print(f"  Total processes: {num_processes}")
    print(f"  Successful processes: {len(successful_results)}")
    print(f"  Failed processes: {len(failed_results)}")
    print(f"  Total time (including build): {total_time:.3f} seconds")
    print(f"  Average build time per process: {avg_build_time:.3f} seconds")
    print(f"  Total successful queries: {total_successful_queries}")
    print(f"  Search QPS (excluding build): {qps:.1f}")
    print(f"  Average query time: {avg_query_time*1000:.3f} ms")
    print(f"  Min query time: {min_query_time*1000:.3f} ms")
    print(f"  Max query time: {max_query_time*1000:.3f} ms")
    
    if failed_results:
        print(f"  Errors: {[r['error'] for r in failed_results]}")
    
    return {
        'num_processes': num_processes,
        'total_time': total_time,
        'avg_build_time': avg_build_time,
        'total_successful_queries': total_successful_queries,
        'qps': qps,
        'avg_query_time': avg_query_time,
        'min_query_time': min_query_time,
        'max_query_time': max_query_time,
        'successful_processes': len(successful_results),
        'failed_processes': len(failed_results)
    }


def compare_concurrency_models():
    """Compare multi-threading vs multi-processing"""
    print("HNSW Concurrency Models Comparison")
    print("=" * 50)
    
    # Load smaller dataset for faster comparison
    max_database_size = 20000
    max_queries = 200
    
    print("Loading data...")
    try:
        base_vectors = load_npy_data("../base.npy", max_vectors=max_database_size)
        query_vectors = load_npy_data("../query.npy", max_vectors=max_queries)
    except Exception as e:
        print(f"Error loading data: {e}")
        return 1
    
    print(f"Dataset: {len(base_vectors):,} vectors × {base_vectors.shape[1]} dimensions")
    print(f"Queries: {len(query_vectors):,} vectors")
    print(f"CPU cores: {mp.cpu_count()}")
    
    # Test configurations
    test_configs = [1, 2, 4]
    queries_per_worker = 50
    
    print(f"\n{'='*80}")
    print("Multi-Process Performance Test")
    print(f"{'='*80}")
    print(f"Workers | QPS    | Avg Query(ms) | Build Time(s) | Total Time(s)")
    print("-" * 70)
    
    mp_results = []
    for num_processes in test_configs:
        result = multiprocess_benchmark(
            base_vectors, query_vectors, 
            num_processes, queries_per_worker
        )
        mp_results.append(result)
        
        print(f"{num_processes:7d} | {result['qps']:6.1f} | {result['avg_query_time']*1000:13.3f} | "
              f"{result['avg_build_time']:13.2f} | {result['total_time']:12.2f}")
    
    # Summary and recommendations
    print(f"\n{'='*80}")
    print("Concurrency Models Comparison Summary")
    print(f"{'='*80}")
    
    print(f"\nKey Observations:")
    print(f"1. Multi-Processing Benefits:")
    print(f"   - Avoids Python GIL limitations")
    print(f"   - True parallel execution")
    print(f"   - Independent memory spaces")
    
    print(f"\n2. Multi-Processing Costs:")
    print(f"   - Each process builds its own index")
    print(f"   - Higher memory usage")
    print(f"   - Process startup overhead")
    
    print(f"\n3. Recommendations:")
    if mp_results:
        best_mp = max(mp_results, key=lambda x: x['qps'])
        print(f"   - Best multi-process config: {best_mp['num_processes']} processes")
        print(f"   - Peak QPS: {best_mp['qps']:.1f}")
        print(f"   - Consider index sharing for memory efficiency")
    
    print(f"\n4. Production Considerations:")
    print(f"   - Use shared memory for index data")
    print(f"   - Pre-built indices to avoid build overhead")
    print(f"   - Load balancing across processes")
    print(f"   - Consider containerization for isolation")
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(compare_concurrency_models())
