#!/usr/bin/env python3
"""
Parameter tuning script for HNSW on Cohere dataset
"""

import time
import numpy as np
from numpy_hnsw_benchmark import NumpyHNSW, load_npy_data, brute_force_search_numpy, calculate_recall


def test_hnsw_parameters(base_vectors, query_vectors, param_combinations, k=10):
    """Test different HNSW parameter combinations"""
    
    print("HNSW Parameter Tuning Results")
    print("=" * 80)
    print(f"Dataset: {base_vectors.shape[0]} vectors × {base_vectors.shape[1]} dimensions")
    print(f"Queries: {query_vectors.shape[0]} vectors")
    print(f"k: {k}")
    print()
    
    # Compute ground truth for a subset of queries
    max_gt_queries = min(20, len(query_vectors))
    print(f"Computing ground truth for {max_gt_queries} queries...")
    ground_truth = []
    for i in range(max_gt_queries):
        gt = brute_force_search_numpy(query_vectors[i], base_vectors, k)
        ground_truth.append(gt)
    print("Ground truth computed.\n")
    
    results = []
    
    print("M  | ef_const | ef_search | Build(s) | Query(ms) | Recall@{} | QPS".format(k))
    print("-" * 70)
    
    for M, ef_construction, ef_search in param_combinations:
        try:
            # Build index
            start_time = time.time()
            hnsw = NumpyHNSW(base_vectors, M=M, max_connections=M*2, ef_construction=ef_construction)
            build_time = time.time() - start_time
            
            # Test search
            total_time = 0
            total_recall = 0
            
            for i in range(len(query_vectors)):
                start_time = time.time()
                hnsw_results = hnsw.search(query_vectors[i], k, ef_search)
                end_time = time.time()
                total_time += (end_time - start_time)
                
                # Calculate recall for ground truth queries
                if i < max_gt_queries:
                    recall = calculate_recall(hnsw_results, ground_truth[i])
                    total_recall += recall
            
            avg_time_ms = (total_time / len(query_vectors)) * 1000
            avg_recall = total_recall / max_gt_queries if max_gt_queries > 0 else 0
            qps = len(query_vectors) / total_time if total_time > 0 else 0
            
            print(f"{M:2d} | {ef_construction:8d} | {ef_search:9d} | {build_time:8.2f} | {avg_time_ms:9.3f} | {avg_recall:.4f} | {qps:7.1f}")
            
            results.append({
                'M': M,
                'ef_construction': ef_construction,
                'ef_search': ef_search,
                'build_time': build_time,
                'query_time_ms': avg_time_ms,
                'recall': avg_recall,
                'qps': qps
            })
            
        except Exception as e:
            print(f"{M:2d} | {ef_construction:8d} | {ef_search:9d} | ERROR: {str(e)[:30]}")
    
    return results


def main():
    """Main parameter tuning function"""
    print("HNSW Parameter Tuning on Cohere Dataset")
    print("=" * 50)
    
    # Load smaller dataset for faster tuning
    max_database_size = 10000
    max_queries = 200
    
    print("Loading data...")
    try:
        base_vectors = load_npy_data("../base.npy", max_vectors=max_database_size)
        query_vectors = load_npy_data("../query.npy", max_vectors=max_queries)
    except Exception as e:
        print(f"Error loading data: {e}")
        return 1
    
    # Define parameter combinations to test
    param_combinations = [
        # (M, ef_construction, ef_search)
        (8, 40, 16),
        (8, 40, 32),
        (8, 40, 64),
        (16, 40, 16),
        (16, 40, 32),
        (16, 40, 64),
        (16, 80, 16),
        (16, 80, 32),
        (16, 80, 64),
        (32, 80, 16),
        (32, 80, 32),
        (32, 80, 64),
        (32, 160, 32),
        (32, 160, 64),
        (32, 160, 128),
    ]
    
    # Run parameter tuning
    results = test_hnsw_parameters(base_vectors, query_vectors, param_combinations)
    
    # Find best configurations
    print("\n" + "=" * 80)
    print("Best Configurations:")
    print("=" * 80)
    
    # Best recall
    best_recall = max(results, key=lambda x: x['recall'])
    print(f"Best Recall: {best_recall['recall']:.4f}")
    print(f"  Parameters: M={best_recall['M']}, ef_construction={best_recall['ef_construction']}, ef_search={best_recall['ef_search']}")
    print(f"  Performance: {best_recall['query_time_ms']:.3f}ms, {best_recall['qps']:.1f} QPS")
    
    # Best QPS with reasonable recall (>0.1)
    good_recall_results = [r for r in results if r['recall'] > 0.1]
    if good_recall_results:
        best_qps = max(good_recall_results, key=lambda x: x['qps'])
        print(f"\nBest QPS (recall > 0.1): {best_qps['qps']:.1f}")
        print(f"  Parameters: M={best_qps['M']}, ef_construction={best_qps['ef_construction']}, ef_search={best_qps['ef_search']}")
        print(f"  Performance: {best_qps['query_time_ms']:.3f}ms, {best_qps['recall']:.4f} recall")
    
    # Best balance (recall * qps)
    for r in results:
        r['balance_score'] = r['recall'] * r['qps'] / 1000  # Normalize QPS
    
    best_balance = max(results, key=lambda x: x['balance_score'])
    print(f"\nBest Balance (recall × QPS): {best_balance['balance_score']:.3f}")
    print(f"  Parameters: M={best_balance['M']}, ef_construction={best_balance['ef_construction']}, ef_search={best_balance['ef_search']}")
    print(f"  Performance: {best_balance['query_time_ms']:.3f}ms, {best_balance['recall']:.4f} recall, {best_balance['qps']:.1f} QPS")
    
    print("\nParameter tuning completed!")
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
