#!/usr/bin/env python3
"""
Improved HNSW benchmark using numpy for better performance
"""

import time
import sys
import random
import math
import numpy as np


def load_npy_data(file_path, max_vectors=None):
    """Load data from numpy file"""
    print(f"Loading {file_path}")
    data = np.load(file_path)
    if max_vectors is not None:
        data = data[:max_vectors]
    print(f"Loaded {data.shape[0]} vectors of dimension {data.shape[1]}")
    return data


class NumpyHNSW:
    """
    Improved HNSW implementation using numpy for better performance
    """
    
    def __init__(self, vectors, M=16, max_connections=32, ef_construction=200):
        """
        Initialize HNSW index
        
        Args:
            vectors: numpy array of vectors to index
            M: Number of bi-directional links for each node
            max_connections: Maximum connections per node
            ef_construction: Size of dynamic candidate list during construction
        """
        self.vectors = vectors
        self.M = M
        self.max_connections = max_connections
        self.ef_construction = ef_construction
        
        # Normalize vectors for cosine similarity
        self.normalized_vectors = vectors / np.linalg.norm(vectors, axis=1, keepdims=True)
        
        # Build graph
        self.graph = {}
        self.build_index()
    
    def build_index(self):
        """Build the HNSW index using numpy operations"""
        print(f"Building HNSW index for {len(self.vectors)} vectors...")
        print(f"Parameters: M={self.M}, max_connections={self.max_connections}")
        
        start_time = time.time()
        n_vectors = len(self.vectors)
        
        # Initialize graph
        for i in range(n_vectors):
            self.graph[i] = set()
        
        # Build connections using vectorized operations
        for i in range(n_vectors):
            if i % 1000 == 0:
                print(f"Processing vector {i+1}/{n_vectors}")
            
            # Calculate similarities to all previous vectors
            if i > 0:
                # Use cosine similarity (dot product of normalized vectors)
                similarities = np.dot(self.normalized_vectors[:i], self.normalized_vectors[i])
                
                # Get top M most similar vectors
                if len(similarities) > self.M:
                    top_indices = np.argpartition(similarities, -self.M)[-self.M:]
                else:
                    top_indices = np.arange(len(similarities))
                
                # Add bidirectional connections
                for j in top_indices:
                    self.graph[i].add(j)
                    self.graph[j].add(i)
                    
                    # Limit connections per node
                    if len(self.graph[j]) > self.max_connections:
                        # Remove connection with lowest similarity
                        connections = list(self.graph[j])
                        if len(connections) > 1:
                            # Calculate similarities for all connections of node j
                            conn_sims = np.dot(self.normalized_vectors[connections], 
                                             self.normalized_vectors[j])
                            worst_idx = connections[np.argmin(conn_sims)]
                            self.graph[j].remove(worst_idx)
                            self.graph[worst_idx].discard(j)
        
        build_time = time.time() - start_time
        print(f"Index built in {build_time:.2f} seconds")
        
        # Print statistics
        total_connections = sum(len(connections) for connections in self.graph.values())
        avg_connections = total_connections / len(self.graph) / 2
        print(f"Average connections per node: {avg_connections:.2f}")
    
    def search(self, query_vector, k=10, ef_search=50):
        """
        Search for k nearest neighbors using graph traversal
        """
        if len(self.vectors) == 0:
            return []
        
        # Normalize query vector
        query_norm = query_vector / np.linalg.norm(query_vector)
        
        # Start with random entry point
        entry_point = random.randint(0, len(self.vectors) - 1)
        
        # Greedy search
        visited = set()
        candidates = []
        
        # Calculate similarity to entry point
        sim = np.dot(self.normalized_vectors[entry_point], query_norm)
        candidates.append((-sim, entry_point))  # Use negative for min-heap behavior
        visited.add(entry_point)
        
        # Expand search
        for _ in range(ef_search):
            if not candidates:
                break
            
            # Get best candidate
            candidates.sort()
            current_sim, current_node = candidates.pop(0)
            
            # Explore neighbors
            for neighbor in self.graph.get(current_node, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    neighbor_sim = np.dot(self.normalized_vectors[neighbor], query_norm)
                    candidates.append((-neighbor_sim, neighbor))
            
            # Keep only best candidates
            candidates.sort()
            candidates = candidates[:ef_search]
        
        # Return top k results (convert back to positive similarities)
        candidates.sort()
        results = [(-sim, idx) for sim, idx in candidates[:k]]
        return results


def brute_force_search_numpy(query_vector, database_vectors, k=10):
    """Brute force k-NN search using numpy"""
    # Normalize vectors
    query_norm = query_vector / np.linalg.norm(query_vector)
    db_norms = database_vectors / np.linalg.norm(database_vectors, axis=1, keepdims=True)
    
    # Calculate cosine similarities
    similarities = np.dot(db_norms, query_norm)
    
    # Get top k indices
    top_k_indices = np.argpartition(similarities, -k)[-k:]
    top_k_indices = top_k_indices[np.argsort(similarities[top_k_indices])[::-1]]
    
    # Return results
    results = [(similarities[i], i) for i in top_k_indices]
    return results


def calculate_recall(hnsw_results, brute_force_results):
    """Calculate recall between HNSW and brute force results"""
    hnsw_indices = set(idx for _, idx in hnsw_results)
    bf_indices = set(idx for _, idx in brute_force_results)
    
    intersection = len(hnsw_indices.intersection(bf_indices))
    return intersection / len(bf_indices) if bf_indices else 0


def benchmark_numpy_hnsw(base_vectors, query_vectors, k=10):
    """Benchmark HNSW vs brute force search using numpy"""
    print("=" * 60)
    print("Numpy HNSW Benchmark on Cohere Data")
    print("=" * 60)
    
    print(f"Database size: {base_vectors.shape[0]} vectors")
    print(f"Query size: {query_vectors.shape[0]} vectors")
    print(f"Vector dimension: {base_vectors.shape[1]}")
    print(f"k: {k}")
    
    # Build HNSW index
    print("\nBuilding HNSW index...")
    hnsw = NumpyHNSW(base_vectors, M=16, max_connections=32, ef_construction=100)
    
    # Test different ef_search values
    ef_search_values = [16, 32, 64, 128]
    
    print(f"\nTesting HNSW search with different ef_search values:")
    print("ef_search | time(ms) | recall@{} | QPS".format(k))
    print("-" * 45)
    
    # Get ground truth for subset of queries
    max_gt_queries = min(50, len(query_vectors))
    ground_truth = []
    
    print(f"\nComputing ground truth for {max_gt_queries} queries...")
    gt_start = time.time()
    for i in range(max_gt_queries):
        if i % 10 == 0:
            print(f"Ground truth query {i+1}/{max_gt_queries}")
        gt = brute_force_search_numpy(query_vectors[i], base_vectors, k)
        ground_truth.append(gt)
    gt_time = time.time() - gt_start
    print(f"Ground truth computed in {gt_time:.2f} seconds")
    
    # Test HNSW with different ef_search values
    for ef_search in ef_search_values:
        print(f"\nTesting ef_search = {ef_search}")
        
        total_time = 0
        total_recall = 0
        
        for i in range(len(query_vectors)):
            if i % 100 == 0:
                print(f"Query {i+1}/{len(query_vectors)}")
            
            start_time = time.time()
            hnsw_results = hnsw.search(query_vectors[i], k, ef_search)
            end_time = time.time()
            
            total_time += (end_time - start_time)
            
            # Calculate recall for queries where we have ground truth
            if i < max_gt_queries:
                recall = calculate_recall(hnsw_results, ground_truth[i])
                total_recall += recall
        
        avg_time_ms = (total_time / len(query_vectors)) * 1000
        avg_recall = total_recall / max_gt_queries if max_gt_queries > 0 else 0
        qps = len(query_vectors) / total_time if total_time > 0 else 0
        
        print(f"{ef_search:8d} | {avg_time_ms:8.3f} | {avg_recall:.4f} | {qps:8.1f}")
    
    # Compare with brute force on small subset
    print(f"\nBrute force baseline (first {max_gt_queries} queries):")
    bf_time_per_query = gt_time / max_gt_queries * 1000
    bf_qps = max_gt_queries / gt_time
    print(f"Brute force | {bf_time_per_query:8.3f} | 1.0000 | {bf_qps:8.1f}")


def main():
    """Main benchmark function"""
    print("Numpy HNSW Benchmark")
    print("=" * 50)
    
    # Configuration
    max_database_size = 50000  # Increased for better performance testing
    max_queries = 1000
    k = 10
    
    # Load data
    print("Loading Cohere data...")
    try:
        base_vectors = load_npy_data("../base.npy", max_vectors=max_database_size)
        query_vectors = load_npy_data("../query.npy", max_vectors=max_queries)
    except Exception as e:
        print(f"Error loading data: {e}")
        return 1
    
    # Run benchmark
    benchmark_numpy_hnsw(base_vectors, query_vectors, k)
    
    print("\nBenchmark completed!")
    print("\nNote: This is an educational HNSW implementation.")
    print("For production use, consider:")
    print("- Faiss (Facebook AI Similarity Search)")
    print("- hnswlib (fast approximate nearest neighbor search)")
    print("- Annoy (Approximate Nearest Neighbors Oh Yeah)")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
