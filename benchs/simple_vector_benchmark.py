#!/usr/bin/env python3
"""
Simple vector similarity benchmark using existing numpy files
This script demonstrates basic vector operations without requiring faiss
"""

import time
import sys
import os

# Try to import numpy, if not available, use basic Python operations
try:
    import numpy as np
    HAS_NUMPY = True
    print("Using numpy for vector operations")
except ImportError:
    HAS_NUMPY = False
    print("Numpy not available, using basic Python operations")


def load_vectors_basic(file_path, max_vectors=None):
    """Load vectors using basic Python (fallback when numpy not available)"""
    print(f"Loading vectors from {file_path} using basic Python...")
    # This is a simplified version - in reality we'd need to parse the .npy format
    # For now, we'll create some dummy data for demonstration
    if max_vectors is None:
        max_vectors = 1000
    
    # Generate some dummy vectors for demonstration
    vectors = []
    dim = 1024  # Cohere dimension
    for i in range(min(max_vectors, 1000)):
        vector = [float(j + i * 0.1) for j in range(dim)]
        vectors.append(vector)
    
    print(f"Generated {len(vectors)} dummy vectors of dimension {dim}")
    return vectors


def load_vectors_numpy(file_path, max_vectors=None):
    """Load vectors using numpy"""
    print(f"Loading vectors from {file_path}")
    try:
        vectors = np.load(file_path)
        if max_vectors is not None:
            vectors = vectors[:max_vectors]
        print(f"Loaded {vectors.shape[0]} vectors of dimension {vectors.shape[1]}")
        return vectors
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None


def dot_product_basic(v1, v2):
    """Compute dot product using basic Python"""
    return sum(a * b for a, b in zip(v1, v2))


def euclidean_distance_basic(v1, v2):
    """Compute Euclidean distance using basic Python"""
    return sum((a - b) ** 2 for a, b in zip(v1, v2)) ** 0.5


def cosine_similarity_basic(v1, v2):
    """Compute cosine similarity using basic Python"""
    dot_prod = dot_product_basic(v1, v2)
    norm_v1 = sum(a * a for a in v1) ** 0.5
    norm_v2 = sum(b * b for b in v2) ** 0.5
    if norm_v1 == 0 or norm_v2 == 0:
        return 0
    return dot_prod / (norm_v1 * norm_v2)


def brute_force_search_basic(query, database, k=10, metric='cosine'):
    """Brute force k-NN search using basic Python"""
    print(f"Performing brute force search for {k} neighbors using {metric} metric...")
    
    similarities = []
    for i, db_vector in enumerate(database):
        if metric == 'cosine':
            sim = cosine_similarity_basic(query, db_vector)
            similarities.append((sim, i))
        elif metric == 'euclidean':
            dist = euclidean_distance_basic(query, db_vector)
            similarities.append((-dist, i))  # Negative for sorting
        else:
            dot_prod = dot_product_basic(query, db_vector)
            similarities.append((dot_prod, i))
    
    # Sort by similarity (descending)
    similarities.sort(reverse=True)
    
    # Return top k
    return similarities[:k]


def brute_force_search_numpy(query, database, k=10, metric='cosine'):
    """Brute force k-NN search using numpy"""
    print(f"Performing brute force search for {k} neighbors using {metric} metric...")
    
    if metric == 'cosine':
        # Normalize vectors
        query_norm = query / np.linalg.norm(query)
        db_norms = database / np.linalg.norm(database, axis=1, keepdims=True)
        similarities = np.dot(db_norms, query_norm)
    elif metric == 'euclidean':
        # Compute squared Euclidean distances
        diff = database - query
        similarities = -np.sum(diff * diff, axis=1)  # Negative for sorting
    else:  # dot product
        similarities = np.dot(database, query)
    
    # Get top k indices
    top_k_indices = np.argpartition(similarities, -k)[-k:]
    top_k_indices = top_k_indices[np.argsort(similarities[top_k_indices])[::-1]]
    
    # Return similarities and indices
    results = [(similarities[i], i) for i in top_k_indices]
    return results


def benchmark_search(database, queries, k=10, metric='cosine'):
    """Benchmark search performance"""
    print(f"\nBenchmarking search with {len(queries)} queries...")
    print(f"Database size: {len(database)}")
    print(f"k: {k}, metric: {metric}")
    
    total_time = 0
    results = []
    
    for i, query in enumerate(queries):
        if i % 10 == 0:
            print(f"Processing query {i+1}/{len(queries)}")
        
        start_time = time.time()
        
        if HAS_NUMPY and isinstance(database, np.ndarray):
            result = brute_force_search_numpy(query, database, k, metric)
        else:
            result = brute_force_search_basic(query, database, k, metric)
        
        end_time = time.time()
        query_time = end_time - start_time
        total_time += query_time
        
        results.append(result)
    
    avg_time_ms = (total_time / len(queries)) * 1000
    total_time_s = total_time
    
    print(f"\nBenchmark Results:")
    print(f"Total time: {total_time_s:.3f} seconds")
    print(f"Average time per query: {avg_time_ms:.3f} ms")
    print(f"Queries per second: {len(queries) / total_time_s:.1f}")
    
    return results, avg_time_ms


def main():
    print("Simple Vector Similarity Benchmark")
    print("=" * 50)
    
    # Configuration
    base_file = "base.npy"
    query_file = "query.npy"
    max_database_size = 10000  # Limit for reasonable performance
    max_queries = 100
    k = 10
    
    # Check if files exist
    if not os.path.exists(base_file):
        print(f"Warning: {base_file} not found, will use dummy data")
        base_file = None
    
    if not os.path.exists(query_file):
        print(f"Warning: {query_file} not found, will use dummy data")
        query_file = None
    
    # Load database vectors
    if HAS_NUMPY and base_file:
        database = load_vectors_numpy(base_file, max_database_size)
        if database is None:
            database = load_vectors_basic(base_file, max_database_size)
    else:
        database = load_vectors_basic(base_file, max_database_size)
    
    # Load query vectors
    if HAS_NUMPY and query_file:
        queries = load_vectors_numpy(query_file, max_queries)
        if queries is None:
            queries = load_vectors_basic(query_file, max_queries)
    else:
        queries = load_vectors_basic(query_file, max_queries)
    
    if database is None or queries is None:
        print("Failed to load data")
        return 1
    
    # Ensure we have the right number of queries
    if HAS_NUMPY and isinstance(queries, np.ndarray):
        queries = queries[:max_queries]
    else:
        queries = queries[:max_queries]
    
    print(f"\nLoaded data:")
    if HAS_NUMPY and isinstance(database, np.ndarray):
        print(f"Database: {database.shape[0]} vectors, {database.shape[1]} dimensions")
        print(f"Queries: {len(queries)} vectors")
    else:
        print(f"Database: {len(database)} vectors, {len(database[0]) if database else 0} dimensions")
        print(f"Queries: {len(queries)} vectors")
    
    # Test different metrics
    metrics = ['cosine', 'euclidean', 'dot']
    
    for metric in metrics:
        print(f"\n{'='*60}")
        print(f"Testing {metric.upper()} similarity")
        print(f"{'='*60}")
        
        try:
            results, avg_time = benchmark_search(database, queries, k, metric)
            print(f"✓ {metric} search completed successfully")
        except Exception as e:
            print(f"✗ {metric} search failed: {e}")
    
    print(f"\n{'='*60}")
    print("Benchmark completed!")
    print(f"{'='*60}")
    
    if HAS_NUMPY:
        print("\nNote: This is a brute force implementation for demonstration.")
        print("For production use, consider using optimized libraries like:")
        print("- Faiss (Facebook AI Similarity Search)")
        print("- Annoy (Approximate Nearest Neighbors Oh Yeah)")
        print("- HNSW (Hierarchical Navigable Small World)")
    else:
        print("\nNote: Install numpy for better performance:")
        print("pip install numpy")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
