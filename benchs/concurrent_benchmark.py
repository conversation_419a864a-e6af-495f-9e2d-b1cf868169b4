#!/usr/bin/env python3
"""
Concurrent HNSW benchmark to test multi-threading performance
"""

import time
import threading
import queue
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp
from numpy_hnsw_benchmark import NumpyHNSW, load_npy_data


class ConcurrentBenchmark:
    """Benchmark HNSW performance under concurrent load"""
    
    def __init__(self, base_vectors, query_vectors):
        self.base_vectors = base_vectors
        self.query_vectors = query_vectors
        self.hnsw_index = None
        self.results_queue = queue.Queue()
        
    def build_index(self, M=16, ef_construction=80):
        """Build HNSW index for concurrent testing"""
        print(f"Building HNSW index for concurrent testing...")
        print(f"Parameters: M={M}, ef_construction={ef_construction}")
        
        start_time = time.time()
        self.hnsw_index = NumpyHNSW(
            self.base_vectors, 
            M=M, 
            max_connections=M*2, 
            ef_construction=ef_construction
        )
        build_time = time.time() - start_time
        print(f"Index built in {build_time:.2f} seconds")
        return build_time
    
    def single_query_worker(self, query_id, query_vector, k=10, ef_search=32):
        """Worker function for single query execution"""
        thread_id = threading.current_thread().ident
        start_time = time.time()
        
        try:
            results = self.hnsw_index.search(query_vector, k=k, ef_search=ef_search)
            end_time = time.time()
            
            return {
                'query_id': query_id,
                'thread_id': thread_id,
                'execution_time': end_time - start_time,
                'results_count': len(results),
                'success': True,
                'error': None
            }
        except Exception as e:
            end_time = time.time()
            return {
                'query_id': query_id,
                'thread_id': thread_id,
                'execution_time': end_time - start_time,
                'results_count': 0,
                'success': False,
                'error': str(e)
            }
    
    def concurrent_search_test(self, num_threads, queries_per_thread, k=10, ef_search=32):
        """Test concurrent search performance"""
        print(f"\n{'='*60}")
        print(f"Concurrent Search Test: {num_threads} threads, {queries_per_thread} queries each")
        print(f"{'='*60}")
        
        total_queries = num_threads * queries_per_thread
        
        # Prepare queries (cycle through available queries)
        test_queries = []
        for i in range(total_queries):
            query_idx = i % len(self.query_vectors)
            test_queries.append((i, self.query_vectors[query_idx]))
        
        # Execute concurrent searches
        start_time = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # Submit all tasks
            future_to_query = {
                executor.submit(
                    self.single_query_worker, 
                    query_id, 
                    query_vector, 
                    k, 
                    ef_search
                ): query_id 
                for query_id, query_vector in test_queries
            }
            
            # Collect results
            for future in as_completed(future_to_query):
                result = future.result()
                results.append(result)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful_queries = [r for r in results if r['success']]
        failed_queries = [r for r in results if not r['success']]
        
        if successful_queries:
            avg_query_time = np.mean([r['execution_time'] for r in successful_queries])
            min_query_time = np.min([r['execution_time'] for r in successful_queries])
            max_query_time = np.max([r['execution_time'] for r in successful_queries])
            std_query_time = np.std([r['execution_time'] for r in successful_queries])
        else:
            avg_query_time = min_query_time = max_query_time = std_query_time = 0
        
        throughput = len(successful_queries) / total_time if total_time > 0 else 0
        
        print(f"Results:")
        print(f"  Total queries: {total_queries}")
        print(f"  Successful: {len(successful_queries)}")
        print(f"  Failed: {len(failed_queries)}")
        print(f"  Total time: {total_time:.3f} seconds")
        print(f"  Throughput: {throughput:.1f} QPS")
        print(f"  Avg query time: {avg_query_time*1000:.3f} ms")
        print(f"  Min query time: {min_query_time*1000:.3f} ms")
        print(f"  Max query time: {max_query_time*1000:.3f} ms")
        print(f"  Std query time: {std_query_time*1000:.3f} ms")
        
        if failed_queries:
            print(f"  Errors: {set(r['error'] for r in failed_queries)}")
        
        return {
            'num_threads': num_threads,
            'queries_per_thread': queries_per_thread,
            'total_queries': total_queries,
            'successful_queries': len(successful_queries),
            'failed_queries': len(failed_queries),
            'total_time': total_time,
            'throughput': throughput,
            'avg_query_time': avg_query_time,
            'min_query_time': min_query_time,
            'max_query_time': max_query_time,
            'std_query_time': std_query_time
        }
    
    def scalability_test(self, max_threads=None, queries_per_thread=50, k=10, ef_search=32):
        """Test scalability with increasing number of threads"""
        if max_threads is None:
            max_threads = min(mp.cpu_count(), 16)  # Limit to reasonable number
        
        print(f"\n{'='*60}")
        print(f"Scalability Test: 1 to {max_threads} threads")
        print(f"{'='*60}")
        
        thread_counts = [1, 2, 4, 8]
        if max_threads > 8:
            thread_counts.extend([12, 16])
        if max_threads > 16:
            thread_counts.append(max_threads)
        
        # Remove duplicates and sort
        thread_counts = sorted(list(set([t for t in thread_counts if t <= max_threads])))
        
        results = []
        baseline_throughput = None
        
        print(f"Threads | QPS    | Speedup | Avg(ms) | Max(ms) | Efficiency")
        print("-" * 60)
        
        for num_threads in thread_counts:
            result = self.concurrent_search_test(
                num_threads, queries_per_thread, k, ef_search
            )
            
            if baseline_throughput is None:
                baseline_throughput = result['throughput']
                speedup = 1.0
            else:
                speedup = result['throughput'] / baseline_throughput if baseline_throughput > 0 else 0
            
            efficiency = speedup / num_threads * 100 if num_threads > 0 else 0
            
            print(f"{num_threads:7d} | {result['throughput']:6.1f} | {speedup:7.2f} | "
                  f"{result['avg_query_time']*1000:7.3f} | {result['max_query_time']*1000:7.3f} | "
                  f"{efficiency:8.1f}%")
            
            results.append({
                **result,
                'speedup': speedup,
                'efficiency': efficiency
            })
        
        return results
    
    def stress_test(self, duration_seconds=30, num_threads=4, k=10, ef_search=32):
        """Stress test with continuous load for specified duration"""
        print(f"\n{'='*60}")
        print(f"Stress Test: {num_threads} threads for {duration_seconds} seconds")
        print(f"{'='*60}")
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        results = []
        query_counter = 0
        
        def stress_worker():
            nonlocal query_counter
            while time.time() < end_time:
                query_idx = query_counter % len(self.query_vectors)
                query_counter += 1
                
                result = self.single_query_worker(
                    query_counter, 
                    self.query_vectors[query_idx], 
                    k, 
                    ef_search
                )
                results.append(result)
        
        # Start stress test
        threads = []
        for i in range(num_threads):
            thread = threading.Thread(target=stress_worker)
            thread.start()
            threads.append(thread)
        
        # Monitor progress
        last_count = 0
        while time.time() < end_time:
            time.sleep(5)  # Report every 5 seconds
            current_count = len(results)
            current_time = time.time() - start_time
            current_qps = (current_count - last_count) / 5 if current_time > 0 else 0
            print(f"  {current_time:6.1f}s: {current_count:6d} queries, {current_qps:6.1f} QPS")
            last_count = current_count
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        successful_queries = [r for r in results if r['success']]
        
        print(f"\nStress Test Results:")
        print(f"  Duration: {total_time:.1f} seconds")
        print(f"  Total queries: {len(results)}")
        print(f"  Successful: {len(successful_queries)}")
        print(f"  Average QPS: {len(successful_queries) / total_time:.1f}")
        
        if successful_queries:
            avg_time = np.mean([r['execution_time'] for r in successful_queries])
            print(f"  Average query time: {avg_time*1000:.3f} ms")
        
        return {
            'duration': total_time,
            'total_queries': len(results),
            'successful_queries': len(successful_queries),
            'average_qps': len(successful_queries) / total_time,
            'results': results
        }


def main():
    """Main concurrent benchmark function"""
    print("HNSW Concurrent Performance Benchmark")
    print("=" * 50)
    
    # Load data (use smaller dataset for faster testing)
    max_database_size = 50000
    max_queries = 500
    
    print("Loading data...")
    try:
        base_vectors = load_npy_data("../base.npy", max_vectors=max_database_size)
        query_vectors = load_npy_data("../query.npy", max_vectors=max_queries)
    except Exception as e:
        print(f"Error loading data: {e}")
        return 1
    
    # Initialize benchmark
    benchmark = ConcurrentBenchmark(base_vectors, query_vectors)
    
    # Build index
    build_time = benchmark.build_index(M=16, ef_construction=80)
    
    # Run concurrent tests
    print(f"\nSystem info:")
    print(f"  CPU cores: {mp.cpu_count()}")
    print(f"  Database size: {len(base_vectors):,} vectors")
    print(f"  Query set size: {len(query_vectors):,} vectors")
    
    # Test 1: Basic concurrent search
    benchmark.concurrent_search_test(
        num_threads=4, 
        queries_per_thread=100, 
        k=10, 
        ef_search=32
    )
    
    # Test 2: Scalability test
    scalability_results = benchmark.scalability_test(
        max_threads=min(mp.cpu_count(), 16),
        queries_per_thread=50,
        k=10,
        ef_search=32
    )
    
    # Test 3: Stress test
    benchmark.stress_test(
        duration_seconds=30,
        num_threads=4,
        k=10,
        ef_search=32
    )
    
    print(f"\n{'='*60}")
    print("Concurrent Benchmark Completed!")
    print(f"{'='*60}")
    
    print(f"\nKey Findings:")
    if scalability_results:
        best_efficiency = max(scalability_results, key=lambda x: x['efficiency'])
        best_throughput = max(scalability_results, key=lambda x: x['throughput'])
        
        print(f"  Best efficiency: {best_efficiency['efficiency']:.1f}% at {best_efficiency['num_threads']} threads")
        print(f"  Best throughput: {best_throughput['throughput']:.1f} QPS at {best_throughput['num_threads']} threads")
        print(f"  Max speedup: {best_throughput['speedup']:.2f}x")
    
    print(f"\nRecommendations:")
    print(f"  - HNSW search operations appear to be thread-safe")
    print(f"  - Optimal thread count depends on workload characteristics")
    print(f"  - Consider thread pool size based on efficiency metrics")
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
