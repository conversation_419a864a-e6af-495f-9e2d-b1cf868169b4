#!/usr/bin/env python3
"""
Cohere 10M HNSW Benchmark using real data
This script implements a simple HNSW-like algorithm for demonstration
"""

import time
import sys
import random
import math
from npy_reader import read_npy_data


def dot_product(v1, v2):
    """Compute dot product"""
    return sum(a * b for a, b in zip(v1, v2))


def euclidean_distance_squared(v1, v2):
    """Compute squared Euclidean distance"""
    return sum((a - b) ** 2 for a, b in zip(v1, v2))


def cosine_similarity(v1, v2):
    """Compute cosine similarity"""
    dot_prod = dot_product(v1, v2)
    norm_v1 = math.sqrt(sum(a * a for a in v1))
    norm_v2 = math.sqrt(sum(b * b for b in v2))
    if norm_v1 == 0 or norm_v2 == 0:
        return 0
    return dot_prod / (norm_v1 * norm_v2)


class SimpleHNSW:
    """
    Simplified HNSW implementation for demonstration
    This is not a full HNSW implementation but demonstrates the concept
    """
    
    def __init__(self, vectors, M=16, max_connections=32, ef_construction=200):
        """
        Initialize HNSW index
        
        Args:
            vectors: List of vectors to index
            M: Number of bi-directional links for each node (HNSW parameter)
            max_connections: Maximum connections per node
            ef_construction: Size of dynamic candidate list during construction
        """
        self.vectors = vectors
        self.M = M
        self.max_connections = max_connections
        self.ef_construction = ef_construction
        
        # Build a simple graph structure
        # For simplicity, we'll create a single-layer graph
        self.graph = {}
        self.build_index()
    
    def build_index(self):
        """Build the HNSW index"""
        print(f"Building HNSW index for {len(self.vectors)} vectors...")
        print(f"Parameters: M={self.M}, max_connections={self.max_connections}")
        
        start_time = time.time()
        
        # Initialize graph
        for i in range(len(self.vectors)):
            self.graph[i] = set()
        
        # Build connections (simplified approach)
        for i in range(len(self.vectors)):
            if i % 1000 == 0:
                print(f"Processing vector {i+1}/{len(self.vectors)}")
            
            # Find nearest neighbors for this vector
            candidates = []
            
            # For efficiency, only consider a subset of vectors
            sample_size = min(self.ef_construction, len(self.vectors))
            if i < sample_size:
                # For early vectors, consider all previous vectors
                candidates = list(range(i))
            else:
                # For later vectors, sample from existing vectors
                candidates = random.sample(range(i), sample_size)
            
            # Calculate distances and find nearest neighbors
            distances = []
            for j in candidates:
                if i != j:
                    dist = euclidean_distance_squared(self.vectors[i], self.vectors[j])
                    distances.append((dist, j))
            
            # Sort by distance and take top M connections
            distances.sort()
            connections = min(self.M, len(distances))
            
            for k in range(connections):
                _, neighbor = distances[k]
                # Add bidirectional connection
                self.graph[i].add(neighbor)
                self.graph[neighbor].add(i)
                
                # Limit connections per node
                if len(self.graph[neighbor]) > self.max_connections:
                    # Remove the farthest connection (simplified)
                    farthest = max(self.graph[neighbor], 
                                 key=lambda x: euclidean_distance_squared(
                                     self.vectors[neighbor], self.vectors[x]))
                    self.graph[neighbor].remove(farthest)
                    self.graph[farthest].discard(neighbor)
        
        build_time = time.time() - start_time
        print(f"Index built in {build_time:.2f} seconds")
        
        # Print statistics
        total_connections = sum(len(connections) for connections in self.graph.values())
        avg_connections = total_connections / len(self.graph) / 2  # Divide by 2 for bidirectional
        print(f"Average connections per node: {avg_connections:.2f}")
    
    def search(self, query_vector, k=10, ef_search=50):
        """
        Search for k nearest neighbors
        
        Args:
            query_vector: Query vector
            k: Number of neighbors to return
            ef_search: Size of dynamic candidate list during search
        
        Returns:
            List of (distance, index) tuples
        """
        if not self.vectors:
            return []
        
        # Start with a random entry point
        entry_point = random.randint(0, len(self.vectors) - 1)
        
        # Greedy search to find local minimum
        visited = set()
        candidates = []
        
        # Calculate distance to entry point
        dist = euclidean_distance_squared(query_vector, self.vectors[entry_point])
        candidates.append((dist, entry_point))
        visited.add(entry_point)
        
        # Expand search using graph connections
        for _ in range(ef_search):
            if not candidates:
                break
            
            # Get closest unvisited candidate
            candidates.sort()
            current_dist, current_node = candidates.pop(0)
            
            # Explore neighbors
            for neighbor in self.graph.get(current_node, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    neighbor_dist = euclidean_distance_squared(query_vector, self.vectors[neighbor])
                    candidates.append((neighbor_dist, neighbor))
            
            # Keep only best candidates
            candidates.sort()
            candidates = candidates[:ef_search]
        
        # Return top k results
        candidates.sort()
        return candidates[:k]


def brute_force_search(query_vector, database_vectors, k=10):
    """Brute force k-NN search for comparison"""
    distances = []
    for i, db_vector in enumerate(database_vectors):
        dist = euclidean_distance_squared(query_vector, db_vector)
        distances.append((dist, i))
    
    distances.sort()
    return distances[:k]


def calculate_recall(hnsw_results, brute_force_results):
    """Calculate recall between HNSW and brute force results"""
    hnsw_indices = set(idx for _, idx in hnsw_results)
    bf_indices = set(idx for _, idx in brute_force_results)
    
    intersection = len(hnsw_indices.intersection(bf_indices))
    return intersection / len(bf_indices) if bf_indices else 0


def benchmark_hnsw(base_vectors, query_vectors, k=10):
    """Benchmark HNSW vs brute force search"""
    print("=" * 60)
    print("HNSW Benchmark on Cohere Data")
    print("=" * 60)
    
    print(f"Database size: {len(base_vectors)} vectors")
    print(f"Query size: {len(query_vectors)} vectors")
    print(f"Vector dimension: {len(base_vectors[0]) if base_vectors else 0}")
    print(f"k: {k}")
    
    # Build HNSW index
    print("\nBuilding HNSW index...")
    hnsw = SimpleHNSW(base_vectors, M=16, max_connections=32, ef_construction=100)
    
    # Test different ef_search values
    ef_search_values = [16, 32, 64, 128]
    
    print(f"\nTesting HNSW search with different ef_search values:")
    print("ef_search | time(ms) | recall | QPS")
    print("-" * 40)
    
    # Get ground truth for first few queries (brute force is expensive)
    max_gt_queries = min(10, len(query_vectors))
    ground_truth = []
    
    print(f"\nComputing ground truth for {max_gt_queries} queries...")
    for i in range(max_gt_queries):
        if i % 5 == 0:
            print(f"Ground truth query {i+1}/{max_gt_queries}")
        gt = brute_force_search(query_vectors[i], base_vectors, k)
        ground_truth.append(gt)
    
    # Test HNSW with different ef_search values
    for ef_search in ef_search_values:
        print(f"\nTesting ef_search = {ef_search}")
        
        total_time = 0
        total_recall = 0
        
        for i in range(len(query_vectors)):
            if i % 100 == 0:
                print(f"Query {i+1}/{len(query_vectors)}")
            
            start_time = time.time()
            hnsw_results = hnsw.search(query_vectors[i], k, ef_search)
            end_time = time.time()
            
            total_time += (end_time - start_time)
            
            # Calculate recall for first few queries where we have ground truth
            if i < max_gt_queries:
                recall = calculate_recall(hnsw_results, ground_truth[i])
                total_recall += recall
        
        avg_time_ms = (total_time / len(query_vectors)) * 1000
        avg_recall = total_recall / max_gt_queries if max_gt_queries > 0 else 0
        qps = len(query_vectors) / total_time if total_time > 0 else 0
        
        print(f"{ef_search:8d} | {avg_time_ms:8.3f} | {avg_recall:.4f} | {qps:6.1f}")


def main():
    """Main benchmark function"""
    print("Cohere 10M HNSW Benchmark")
    print("=" * 50)
    
    # Configuration
    max_database_size = 10000  # Limit for reasonable performance
    max_queries = 100
    k = 10
    
    # Load data
    print("Loading Cohere data...")
    base_vectors = read_npy_data("../base.npy", max_vectors=max_database_size)
    query_vectors = read_npy_data("../query.npy", max_vectors=max_queries)
    
    if not base_vectors or not query_vectors:
        print("Failed to load data")
        return 1
    
    # Run benchmark
    benchmark_hnsw(base_vectors, query_vectors, k)
    
    print("\nBenchmark completed!")
    print("\nNote: This is a simplified HNSW implementation for demonstration.")
    print("For production use, consider using optimized libraries like:")
    print("- Faiss (Facebook AI Similarity Search)")
    print("- hnswlib (fast approximate nearest neighbor search)")
    print("- Annoy (Approximate Nearest Neighbors Oh Yeah)")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
