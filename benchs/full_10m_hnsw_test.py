#!/usr/bin/env python3
"""
Full 10M HNSW test - testing the complete 10 million vector dataset
"""

import time
import sys
import os
import gc
import psutil
import numpy as np
import random
import heapq
import math
from collections import defaultdict


def get_memory_usage():
    """Get current memory usage in GB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024 / 1024


class Full10MHNSW:
    """
    HNSW implementation optimized for 10M vectors
    """
    
    def __init__(self, vectors, M=16, max_M=16, max_M0=32, ef_construction=200):
        """Initialize HNSW for 10M vectors"""
        self.vectors = vectors
        self.M = M
        self.max_M = max_M
        self.max_M0 = max_M0
        self.ef_construction = ef_construction
        
        print(f"Initializing HNSW for {len(vectors):,} vectors (FULL 10M DATASET)")
        print(f"Parameters: M={M}, max_M={max_M}, max_M0={max_M0}, ef_construction={ef_construction}")
        print(f"Expected memory usage: ~60-80 GB")
        print(f"Expected build time: 2-4 hours")
        
        # Normalize vectors
        print("Normalizing vectors...")
        start_time = time.time()
        norms = np.linalg.norm(vectors, axis=1, keepdims=True)
        self.normalized_vectors = vectors / norms
        norm_time = time.time() - start_time
        print(f"Normalization completed in {norm_time:.2f} seconds")
        
        # Graph structure
        self.graph = {}
        self.node_levels = {}
        self.entry_point = None
        self.max_layer = 0
        
    def distance(self, i, j):
        """Calculate distance between two vectors"""
        return 1.0 - np.dot(self.normalized_vectors[i], self.normalized_vectors[j])
    
    def get_random_level(self):
        """Generate random level for new node"""
        level = 0
        while random.random() < 0.5 and level < 16:
            level += 1
        return level
    
    def search_layer(self, query_idx, entry_points, num_closest, layer):
        """Search for num_closest neighbors in a specific layer"""
        visited = set()
        candidates = []
        w = []
        
        # Initialize with entry points
        for ep in entry_points:
            if layer in self.graph and ep in self.graph[layer]:
                dist = self.distance(query_idx, ep)
                heapq.heappush(candidates, (dist, ep))
                heapq.heappush(w, (-dist, ep))
                visited.add(ep)
        
        if not candidates:
            return []
        
        # Search loop
        while candidates:
            current_dist, current = heapq.heappop(candidates)
            
            if w and current_dist > -w[0][0]:
                break
            
            if layer in self.graph and current in self.graph[layer]:
                for neighbor in self.graph[layer][current]:
                    if neighbor not in visited:
                        visited.add(neighbor)
                        dist = self.distance(query_idx, neighbor)
                        
                        if len(w) < num_closest:
                            heapq.heappush(candidates, (dist, neighbor))
                            heapq.heappush(w, (-dist, neighbor))
                        elif dist < -w[0][0]:
                            heapq.heappush(candidates, (dist, neighbor))
                            heapq.heappush(w, (-dist, neighbor))
                            
                            if len(w) > num_closest:
                                heapq.heappop(w)
        
        # Return results
        result = []
        temp_w = []
        while w:
            dist, node = heapq.heappop(w)
            temp_w.append((node, -dist))
        
        return sorted(temp_w, key=lambda x: x[1])[:num_closest]
    
    def select_neighbors_simple(self, candidates, M):
        """Simple neighbor selection"""
        candidates.sort(key=lambda x: x[1])
        return [node for node, dist in candidates[:M]]
    
    def insert_node(self, node_idx):
        """Insert a node into the HNSW graph"""
        level = self.get_random_level()
        self.node_levels[node_idx] = level
        
        # Initialize graph layers for this node
        for lev in range(level + 1):
            if lev not in self.graph:
                self.graph[lev] = {}
            self.graph[lev][node_idx] = set()
        
        # Update max layer and entry point
        if level > self.max_layer:
            self.max_layer = level
            self.entry_point = node_idx
        
        # Handle first node
        if self.entry_point is None:
            self.entry_point = node_idx
            self.max_layer = level
            return
        
        current_nearest = [self.entry_point]
        
        # Search from top layer down to level+1
        for lev in range(self.max_layer, level, -1):
            current_nearest = self.search_layer(node_idx, current_nearest, 1, lev)
            current_nearest = [node for node, dist in current_nearest]
        
        # Search and connect from level down to 0
        for lev in range(min(level, self.max_layer), -1, -1):
            candidates = self.search_layer(node_idx, current_nearest, self.ef_construction, lev)
            
            # Select neighbors
            max_conn = self.max_M0 if lev == 0 else self.max_M
            neighbors = self.select_neighbors_simple(candidates, max_conn)
            
            # Ensure layer exists
            if lev not in self.graph:
                self.graph[lev] = {}
            
            # Add bidirectional connections
            for neighbor in neighbors:
                if neighbor not in self.graph[lev]:
                    self.graph[lev][neighbor] = set()
                
                self.graph[lev][node_idx].add(neighbor)
                self.graph[lev][neighbor].add(node_idx)
                
                # Simple pruning if too many connections
                if len(self.graph[lev][neighbor]) > max_conn:
                    connections = list(self.graph[lev][neighbor])
                    to_remove = random.choice(connections)
                    self.graph[lev][neighbor].remove(to_remove)
                    if to_remove in self.graph[lev] and neighbor in self.graph[lev][to_remove]:
                        self.graph[lev][to_remove].remove(neighbor)
            
            current_nearest = neighbors
    
    def build_index_10m(self):
        """Build HNSW index for full 10M dataset"""
        print(f"\n{'='*80}")
        print("BUILDING HNSW INDEX FOR FULL 10M DATASET")
        print(f"{'='*80}")
        print(f"Dataset size: {len(self.vectors):,} vectors")
        print(f"Vector dimension: {self.vectors.shape[1]}")
        print(f"Total data size: {self.vectors.nbytes / 1024**3:.2f} GB")
        print(f"Starting memory: {get_memory_usage():.2f} GB")
        
        start_time = time.time()
        n_vectors = len(self.vectors)
        
        # Progress tracking
        checkpoint_interval = 10000  # Report every 10K vectors
        last_checkpoint_time = start_time
        
        for i in range(n_vectors):
            if i % checkpoint_interval == 0:
                current_time = time.time()
                elapsed = current_time - start_time
                
                if i > 0:
                    # Calculate rates
                    vectors_per_second = i / elapsed
                    eta_seconds = (n_vectors - i) / vectors_per_second
                    eta_hours = eta_seconds / 3600
                    
                    # Memory usage
                    memory_gb = get_memory_usage()
                    
                    # Progress report
                    progress_pct = (i / n_vectors) * 100
                    print(f"Progress: {i:,}/{n_vectors:,} ({progress_pct:.2f}%) | "
                          f"Speed: {vectors_per_second:.1f} vec/s | "
                          f"Elapsed: {elapsed/3600:.2f}h | "
                          f"ETA: {eta_hours:.2f}h | "
                          f"Memory: {memory_gb:.1f}GB")
                    
                    # Checkpoint timing
                    checkpoint_time = current_time - last_checkpoint_time
                    print(f"  Last {checkpoint_interval:,} vectors took {checkpoint_time:.1f}s "
                          f"({checkpoint_interval/checkpoint_time:.1f} vec/s)")
                    last_checkpoint_time = current_time
                else:
                    print(f"Starting insertion of {n_vectors:,} vectors...")
            
            # Insert the node
            self.insert_node(i)
            
            # Memory check every 100K vectors
            if i % 100000 == 0 and i > 0:
                memory_gb = get_memory_usage()
                if memory_gb > 100:  # Warning if memory usage is very high
                    print(f"WARNING: High memory usage: {memory_gb:.1f}GB at vector {i:,}")
        
        build_time = time.time() - start_time
        build_hours = build_time / 3600
        
        print(f"\n{'='*80}")
        print("10M HNSW INDEX BUILD COMPLETED!")
        print(f"{'='*80}")
        print(f"Total build time: {build_time:.1f} seconds ({build_hours:.2f} hours)")
        print(f"Average speed: {n_vectors/build_time:.1f} vectors/second")
        print(f"Final memory usage: {get_memory_usage():.2f} GB")
        
        # Calculate statistics
        total_connections = 0
        layer_stats = {}
        
        for layer in range(self.max_layer + 1):
            if layer in self.graph:
                layer_nodes = len(self.graph[layer])
                layer_connections = sum(len(neighbors) for neighbors in self.graph[layer].values())
                layer_stats[layer] = (layer_nodes, layer_connections)
                total_connections += layer_connections
            else:
                layer_stats[layer] = (0, 0)
        
        print(f"\nGraph Statistics:")
        print(f"Max layer: {self.max_layer}")
        print(f"Entry point: {self.entry_point}")
        print(f"Total connections: {total_connections // 2:,} (bidirectional)")
        print(f"Average connections per vector: {total_connections / (2 * n_vectors):.2f}")
        
        # Show top layers
        print(f"\nLayer distribution (top 10 layers):")
        for layer in sorted(layer_stats.keys(), reverse=True)[:10]:
            nodes, connections = layer_stats[layer]
            avg_conn = connections / nodes if nodes > 0 else 0
            print(f"Layer {layer:2d}: {nodes:8,} nodes, {connections:10,} connections, {avg_conn:5.2f} avg/node")
        
        return build_time
    
    def search_10m(self, query_vector, k=10, ef_search=50):
        """Search in the 10M index"""
        if self.entry_point is None:
            return []
        
        # Normalize query
        query_norm = query_vector / np.linalg.norm(query_vector)
        
        # Add query as temporary node
        temp_vectors = np.vstack([self.normalized_vectors, query_norm.reshape(1, -1)])
        original_vectors = self.normalized_vectors
        self.normalized_vectors = temp_vectors
        query_idx = len(original_vectors)
        
        try:
            # Search from top layer down
            current_nearest = [self.entry_point]
            for layer in range(self.max_layer, 0, -1):
                current_nearest = self.search_layer(query_idx, current_nearest, 1, layer)
                current_nearest = [node for node, dist in current_nearest]
            
            # Search layer 0
            candidates = self.search_layer(query_idx, current_nearest, max(ef_search, k), 0)
            
            # Return top k
            results = [(dist, idx) for idx, dist in candidates[:k]]
            return results
            
        finally:
            self.normalized_vectors = original_vectors


def test_full_10m_hnsw():
    """Test HNSW on the full 10M dataset"""
    print("FULL 10M HNSW TEST")
    print("=" * 80)
    print("This will test HNSW on the complete 10 million vector dataset")
    print("Expected requirements:")
    print("- Memory: 60-80 GB")
    print("- Time: 2-4 hours")
    print("- Storage: Additional ~20-30 GB for graph structure")
    
    # Confirm before starting
    response = input("\nThis is a very long test. Continue? (yes/no): ")
    if response.lower() != 'yes':
        print("Test cancelled.")
        return
    
    try:
        # Load full dataset
        print(f"\nLoading full 10M dataset...")
        start_time = time.time()
        
        base_vectors = np.load("../base_10m.npy")  # Load full dataset
        query_vectors = np.load("../query_10m.npy")[:100]  # Use 100 queries
        
        load_time = time.time() - start_time
        print(f"Data loaded in {load_time:.2f} seconds")
        print(f"Base vectors: {base_vectors.shape}")
        print(f"Query vectors: {query_vectors.shape}")
        print(f"Memory after loading: {get_memory_usage():.2f} GB")
        
        # Build HNSW index
        hnsw_10m = Full10MHNSW(
            base_vectors,
            M=16,
            max_M=16,
            max_M0=32,
            ef_construction=200
        )
        
        build_time = hnsw_10m.build_index_10m()
        
        # Test search performance
        print(f"\n{'='*80}")
        print("TESTING SEARCH PERFORMANCE ON 10M INDEX")
        print(f"{'='*80}")
        
        search_times = []
        for i, query in enumerate(query_vectors[:50]):  # Test 50 queries
            if i % 10 == 0:
                print(f"Testing query {i+1}/50...")
            
            start_time = time.time()
            results = hnsw_10m.search_10m(query, k=10, ef_search=50)
            search_time = time.time() - start_time
            search_times.append(search_time)
        
        avg_search_time = np.mean(search_times)
        qps = 1.0 / avg_search_time if avg_search_time > 0 else 0
        
        print(f"\n{'='*80}")
        print("FINAL RESULTS FOR 10M HNSW")
        print(f"{'='*80}")
        print(f"Dataset size: {len(base_vectors):,} vectors")
        print(f"Build time: {build_time:.1f} seconds ({build_time/3600:.2f} hours)")
        print(f"Average search time: {avg_search_time*1000:.3f} ms")
        print(f"Search QPS: {qps:.1f}")
        print(f"Final memory usage: {get_memory_usage():.2f} GB")
        print(f"Max layer: {hnsw_10m.max_layer}")
        
        return {
            'size': len(base_vectors),
            'build_time': build_time,
            'search_time_ms': avg_search_time * 1000,
            'qps': qps,
            'memory_gb': get_memory_usage(),
            'max_layer': hnsw_10m.max_layer
        }
        
    except Exception as e:
        print(f"Error in 10M test: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main function"""
    print("Full 10M HNSW Implementation Test")
    print("This will test HNSW on the complete 10 million vector Cohere dataset")
    
    result = test_full_10m_hnsw()
    
    if result:
        print(f"\n10M HNSW test completed successfully!")
        print(f"This demonstrates HNSW performance on a real 10M vector dataset.")
    else:
        print(f"\n10M HNSW test failed or was cancelled.")
    
    return 0 if result else 1


if __name__ == "__main__":
    sys.exit(main())
