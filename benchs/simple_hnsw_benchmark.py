#!/usr/bin/env python3
"""
Simple HNSW benchmark using pre-converted numpy files
"""

import time
import sys
import numpy as np
import os
import argparse

# Try to import faiss
try:
    import faiss
    print("Faiss imported successfully")
except ImportError:
    print("Faiss not found. Please install faiss first.")
    print("You can try: pip install faiss-cpu")
    sys.exit(1)


def load_data(base_path="../base.npy", query_path="../query.npy"):
    """Load base and query vectors from numpy files"""
    print(f"Loading base vectors from {base_path}")
    base_vectors = np.load(base_path)
    print(f"Base vectors shape: {base_vectors.shape}")
    
    print(f"Loading query vectors from {query_path}")
    query_vectors = np.load(query_path)
    print(f"Query vectors shape: {query_vectors.shape}")
    
    return base_vectors, query_vectors


def evaluate_index(index, xq, k=10):
    """
    Evaluate index performance
    
    Args:
        index: Faiss index
        xq: Query vectors
        k: Number of neighbors to search
    
    Returns:
        Tuple of (query_time_ms, distances, indices)
    """
    print(f"Searching {xq.shape[0]} queries for {k} neighbors...")
    
    t0 = time.time()
    D, I = index.search(xq, k)
    t1 = time.time()
    
    nq = xq.shape[0]
    query_time_ms = (t1 - t0) * 1000.0 / nq
    
    # Calculate missing rate
    missing_rate = (I == -1).sum() / float(k * nq)
    
    return query_time_ms, missing_rate, D, I


def bench_hnsw_flat(xb, xq, args):
    """Benchmark HNSW Flat index"""
    print("=" * 60)
    print("Testing HNSW Flat")
    print("=" * 60)
    
    d = xb.shape[1]
    print(f"Dataset: {xb.shape[0]} database vectors, {xq.shape[0]} queries, dimension {d}")
    
    # Limit data size for testing if requested
    if args.max_database_size and args.max_database_size < xb.shape[0]:
        print(f"Limiting database to {args.max_database_size} vectors")
        xb = xb[:args.max_database_size]
    
    if args.max_queries and args.max_queries < xq.shape[0]:
        print(f"Limiting queries to {args.max_queries} vectors")
        xq = xq[:args.max_queries]
    
    # Create HNSW index
    print(f"Creating HNSW index with M={args.hnsw_m}")
    index = faiss.IndexHNSWFlat(d, args.hnsw_m)
    
    # Set construction parameters
    index.hnsw.efConstruction = args.ef_construction
    index.verbose = args.verbose
    
    print(f"Index parameters: M={args.hnsw_m}, efConstruction={args.ef_construction}")
    
    # Add vectors to index
    print("Adding vectors to index...")
    t0 = time.time()
    index.add(xb)
    t1 = time.time()
    
    build_time = t1 - t0
    print(f"Index built in {build_time:.2f} seconds ({xb.shape[0]/build_time:.0f} vectors/sec)")
    
    # Test different efSearch values
    print("\nTesting different efSearch values:")
    print("efSearch | bounded_queue | time(ms) | missing")
    print("-" * 45)
    
    results = []
    
    for efSearch in args.ef_search_values:
        for bounded_queue in [True, False]:
            index.hnsw.efSearch = efSearch
            index.hnsw.search_bounded_queue = bounded_queue
            
            query_time_ms, missing_rate, D, I = evaluate_index(
                index, xq, args.k
            )
            
            print(f"{efSearch:8d} | {str(bounded_queue):13s} | {query_time_ms:8.3f} | {missing_rate:.4f}")
            
            results.append({
                'efSearch': efSearch,
                'bounded_queue': bounded_queue,
                'query_time_ms': query_time_ms,
                'missing_rate': missing_rate,
                'build_time': build_time
            })
    
    return results


def bench_hnsw_sq(xb, xq, args):
    """Benchmark HNSW with Scalar Quantizer"""
    print("=" * 60)
    print("Testing HNSW with Scalar Quantizer")
    print("=" * 60)
    
    d = xb.shape[1]
    print(f"Dataset: {xb.shape[0]} database vectors, {xq.shape[0]} queries, dimension {d}")
    
    # Limit data size for testing if requested
    if args.max_database_size and args.max_database_size < xb.shape[0]:
        print(f"Limiting database to {args.max_database_size} vectors")
        xb = xb[:args.max_database_size]
    
    if args.max_queries and args.max_queries < xq.shape[0]:
        print(f"Limiting queries to {args.max_queries} vectors")
        xq = xq[:args.max_queries]
    
    # Use subset for training
    xt = xb[:min(100000, xb.shape[0])]  # Use up to 100k for training
    
    # Create HNSW with scalar quantizer
    print(f"Creating HNSW+SQ index with M={args.hnsw_m}")
    index = faiss.IndexHNSWSQ(d, faiss.ScalarQuantizer.QT_8bit, args.hnsw_m)
    
    # Set construction parameters
    index.hnsw.efConstruction = args.ef_construction
    index.verbose = args.verbose
    
    print(f"Index parameters: M={args.hnsw_m}, efConstruction={args.ef_construction}, quantizer=8bit")
    
    # Train the scalar quantizer
    print("Training scalar quantizer...")
    t0 = time.time()
    index.train(xt)
    t1 = time.time()
    train_time = t1 - t0
    print(f"Training completed in {train_time:.2f} seconds")
    
    # Add vectors to index
    print("Adding vectors to index...")
    t0 = time.time()
    index.add(xb)
    t1 = time.time()
    
    build_time = t1 - t0
    print(f"Index built in {build_time:.2f} seconds ({xb.shape[0]/build_time:.0f} vectors/sec)")
    
    # Test different efSearch values
    print("\nTesting different efSearch values:")
    print("efSearch | time(ms) | missing")
    print("-" * 30)
    
    results = []
    
    for efSearch in args.ef_search_values:
        index.hnsw.efSearch = efSearch
        
        query_time_ms, missing_rate, D, I = evaluate_index(
            index, xq, args.k
        )
        
        print(f"{efSearch:8d} | {query_time_ms:8.3f} | {missing_rate:.4f}")
        
        results.append({
            'efSearch': efSearch,
            'query_time_ms': query_time_ms,
            'missing_rate': missing_rate,
            'train_time': train_time,
            'build_time': build_time
        })
    
    return results


def main():
    parser = argparse.ArgumentParser(description='Simple HNSW benchmark using numpy files')
    
    # Data parameters
    parser.add_argument('--base-file', default='../base.npy',
                        help='Path to base vectors numpy file')
    parser.add_argument('--query-file', default='../query.npy',
                        help='Path to query vectors numpy file')
    parser.add_argument('--max-database-size', type=int, default=None,
                        help='Maximum number of database vectors to use (for testing)')
    parser.add_argument('--max-queries', type=int, default=None,
                        help='Maximum number of queries to use')
    
    # Search parameters
    parser.add_argument('--k', type=int, default=10,
                        help='Number of nearest neighbors to search')
    
    # HNSW parameters
    parser.add_argument('--hnsw-m', type=int, default=32,
                        help='HNSW M parameter (number of connections)')
    parser.add_argument('--ef-construction', type=int, default=40,
                        help='HNSW efConstruction parameter')
    parser.add_argument('--ef-search-values', type=int, nargs='+', 
                        default=[16, 32, 64, 128],
                        help='List of efSearch values to test')
    
    # Test selection
    parser.add_argument('--tests', nargs='+', default=['hnsw_flat'],
                        choices=['hnsw_flat', 'hnsw_sq'],
                        help='Which tests to run')
    
    # Other parameters
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    
    args = parser.parse_args()
    
    print("Simple HNSW Benchmark")
    print("=" * 60)
    print(f"Arguments: {args}")
    print()
    
    # Load data
    try:
        xb, xq = load_data(args.base_file, args.query_file)
    except Exception as e:
        print(f"Error loading data: {e}")
        return 1
    
    # Run tests
    all_results = {}
    
    if 'hnsw_flat' in args.tests:
        try:
            results = bench_hnsw_flat(xb, xq, args)
            all_results['hnsw_flat'] = results
        except Exception as e:
            print(f"Error in HNSW Flat test: {e}")
    
    if 'hnsw_sq' in args.tests:
        try:
            results = bench_hnsw_sq(xb, xq, args)
            all_results['hnsw_sq'] = results
        except Exception as e:
            print(f"Error in HNSW SQ test: {e}")
    
    print("\nBenchmark completed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
