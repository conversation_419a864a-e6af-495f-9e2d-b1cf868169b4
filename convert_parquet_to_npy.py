import pyarrow.parquet as pq
import numpy as np
import pandas as pd
import os

def parquet_to_numpy(path, max_rows=None):
    print(f"Loading {path}")
    df = pd.read_parquet(path)

    # 找到第一个 ndarray/list 类型的列作为向量列
    for col in df.columns:
        first_val = df[col].iloc[0]
        if isinstance(first_val, (list, np.ndarray)):
            vecs = df[col].tolist()
            arr = np.array(vecs, dtype=np.float32)
            break
    else:
        raise ValueError("没有找到嵌套向量列")

    if max_rows is not None:
        arr = arr[:max_rows]
    return arr

def convert_and_save(paths, output_file, max_rows=None):
    arrays = [parquet_to_numpy(p, max_rows) for p in paths]
    full_array = np.vstack(arrays)
    np.save(output_file, full_array)
    print(f"Saved {output_file}, shape={full_array.shape}, dtype={full_array.dtype}")

# 修改为你的路径 - 加载全部 10M 向量
base_paths = [
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-00-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-01-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-02-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-03-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-04-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-05-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-06-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-07-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-08-of-10.parquet",
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-09-of-10.parquet",
]

query_path = "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/test.parquet"

print("Converting full 10M Cohere dataset...")
print(f"Loading {len(base_paths)} training shards...")
convert_and_save(base_paths, "base_10m.npy")
convert_and_save([query_path], "query_10m.npy")

